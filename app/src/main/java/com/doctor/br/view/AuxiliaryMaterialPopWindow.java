package com.doctor.br.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;

import com.doctor.yy.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 辅料选择弹窗
 * 支持多选辅料和"不添加辅料"选项
 */
public class AuxiliaryMaterialPopWindow extends PopupWindow implements View.OnClickListener {
    private Context mContext;
    private ImageView closeImg;
    private TextView titleTv;
    private LinearLayout materialContainer;
    private CheckBox noMaterialCb;
    private TextView confirmBtn;
    private View backgroundView;
    private LinearLayout contentLayout;
    
    private List<String> materialList; // 辅料列表
    private List<String> selectedMaterials = new ArrayList<>(); // 已选择的辅料
    private boolean isNoMaterial = false; // 是否选择"不添加辅料"
    private OnConfirmListener onConfirmListener;
    
    public interface OnConfirmListener {
        void onConfirm(List<String> selectedMaterials, boolean isNoMaterial);
    }
    
    public AuxiliaryMaterialPopWindow(Context context) {
        super(context);
        mContext = context;
        initView();
    }
    
    public AuxiliaryMaterialPopWindow(Context context, List<String> materialList) {
        super(context);
        mContext = context;
        this.materialList = materialList;
        initView();
    }
    
    private void initView() {
        View contentView = LayoutInflater.from(mContext).inflate(R.layout.auxiliary_material_popwindow_layout, null);
        this.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setHeight(ViewGroup.LayoutParams.MATCH_PARENT);
        this.setContentView(contentView);
        this.setBackgroundDrawable(new BitmapDrawable());
        this.setFocusable(true);
        this.setTouchable(true);
        this.setOutsideTouchable(false);
        // 移除整体弹窗动画，改为自定义动画
        
        findViews(contentView);
        setListeners();
        initData();
    }
    
    private void findViews(View contentView) {
        backgroundView = contentView.findViewById(R.id.container);
        contentLayout = contentView.findViewById(R.id.content_layout);
        closeImg = contentView.findViewById(R.id.close_img);
        titleTv = contentView.findViewById(R.id.title_tv);
        materialContainer = contentView.findViewById(R.id.material_container);
        noMaterialCb = contentView.findViewById(R.id.no_material_cb);
        confirmBtn = contentView.findViewById(R.id.confirm_btn);
        
        titleTv.setText("请选择辅料");
        
        // 设置背景点击事件，点击空白区域关闭弹窗
        backgroundView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismissWithAnimation();
            }
        });
        
        // 设置内容区域点击事件，消费点击事件，防止点击内容区域时关闭弹窗
        contentView.findViewById(R.id.rl_title).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 消费点击事件
            }
        });
    }
    
    private void setListeners() {
        closeImg.setOnClickListener(this);
        confirmBtn.setOnClickListener(this);
        
        noMaterialCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                isNoMaterial = isChecked;
                if (isChecked) {
                    // 选择"不添加辅料"时，清空所有辅料选择
                    selectedMaterials.clear();
                    updateMaterialCheckBoxes();
                }
            }
        });
    }
    
    private void initData() {
        if (materialList != null && !materialList.isEmpty()) {
            createMaterialCheckBoxes();
        }
    }
    
    private void createMaterialCheckBoxes() {
        materialContainer.removeAllViews();
        
        for (String material : materialList) {
            CheckBox checkBox = new CheckBox(mContext);
            checkBox.setText(material);
            checkBox.setTextColor(mContext.getResources().getColor(R.color.br_color_theme_text));
            checkBox.setTextSize(15);
            
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    ViewGroup.LayoutParams.WRAP_CONTENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT);
            params.setMargins(0, 10, 0, 10);
            checkBox.setLayoutParams(params);
            
            checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                    String materialName = buttonView.getText().toString();
                    if (isChecked) {
                        if (!selectedMaterials.contains(materialName)) {
                            selectedMaterials.add(materialName);
                        }
                        // 选择辅料时，取消"不添加辅料"选择
                        if (isNoMaterial) {
                            isNoMaterial = false;
                            noMaterialCb.setChecked(false);
                        }
                    } else {
                        selectedMaterials.remove(materialName);
                    }
                }
            });
            
            materialContainer.addView(checkBox);
        }
    }
    
    private void updateMaterialCheckBoxes() {
        for (int i = 0; i < materialContainer.getChildCount(); i++) {
            View child = materialContainer.getChildAt(i);
            if (child instanceof CheckBox) {
                CheckBox checkBox = (CheckBox) child;
                String materialName = checkBox.getText().toString();
                checkBox.setChecked(selectedMaterials.contains(materialName));
            }
        }
    }
    
    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.close_img) {
            dismissWithAnimation();
        } else if (v.getId() == R.id.confirm_btn) {
            if (onConfirmListener != null) {
                onConfirmListener.onConfirm(selectedMaterials, isNoMaterial);
            }
            dismissWithAnimation();
        }
    }
    
    public void setOnConfirmListener(OnConfirmListener listener) {
        this.onConfirmListener = listener;
    }
    
    public void setMaterialList(List<String> materialList) {
        this.materialList = materialList;
        if (materialList != null && !materialList.isEmpty()) {
            createMaterialCheckBoxes();
        }
    }
    
    public void setSelectedMaterials(List<String> selectedMaterials, boolean isNoMaterial) {
        this.selectedMaterials = new ArrayList<>(selectedMaterials);
        this.isNoMaterial = isNoMaterial;
        if (noMaterialCb != null) {
            noMaterialCb.setChecked(isNoMaterial);
        }
        updateMaterialCheckBoxes();
    }
    
    public void show(View parent) {
        if (!((Activity) mContext).isFinishing()) {
            this.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
            showWithAnimation();
        }
    }
    
    /**
     * 显示弹窗时的动画效果
     */
    private void showWithAnimation() {
        if (backgroundView != null && contentLayout != null) {
            // 背景淡入动画
            Animation fadeIn = AnimationUtils.loadAnimation(mContext, R.anim.fade_in);
            backgroundView.startAnimation(fadeIn);
            
            // 内容从底部滑入动画
            Animation slideIn = AnimationUtils.loadAnimation(mContext, R.anim.slide_in_bottom);
            contentLayout.startAnimation(slideIn);
        }
    }
    
    /**
     * 隐藏弹窗时的动画效果
     */
    private void dismissWithAnimation() {
        if (backgroundView != null && contentLayout != null) {
            // 背景淡出动画
            Animation fadeOut = AnimationUtils.loadAnimation(mContext, R.anim.fade_out);
            // 内容向底部滑出动画
            Animation slideOut = AnimationUtils.loadAnimation(mContext, R.anim.slide_out_bottom);
            
            // 设置动画结束监听，在动画结束后真正关闭弹窗
            fadeOut.setAnimationListener(new Animation.AnimationListener() {
                @Override
                public void onAnimationStart(Animation animation) {}
                
                @Override
                public void onAnimationEnd(Animation animation) {
                    dismiss();
                }
                
                @Override
                public void onAnimationRepeat(Animation animation) {}
            });
            
            backgroundView.startAnimation(fadeOut);
            contentLayout.startAnimation(slideOut);
        } else {
            dismiss();
        }
    }
}