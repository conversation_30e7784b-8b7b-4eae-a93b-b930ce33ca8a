package com.doctor.br.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.doctor.br.db.entity.Contacts;
import com.doctor.br.view.stickyListView.StickyListHeadersAdapter;
import com.doctor.yy.R;

import org.newapp.ones.base.adapter.BaseAdapter;
import org.newapp.ones.base.adapter.ViewHolder;
import org.newapp.ones.base.utils.ImageUtils;
import org.newapp.ones.base.utils.LogUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 转发消息页面适配器
 * @createTime 2017/12/14
 */

public class MsgRepeatAdapter extends BaseAdapter<Contacts> implements StickyListHeadersAdapter {
    private Context mContext;
    private List<Contacts> mList;
    private int headerViewIndex = 0;
    private OnItemCheckedListener onItemCheckedListener;

    public MsgRepeatAdapter(Context context, List<Contacts> list, int layoutResId) {
        super(context, list, layoutResId);
        this.mContext = context;
        this.mList = list;
    }

    public void setOnItemCheckedListener(OnItemCheckedListener onItemCheckedListener) {
        this.onItemCheckedListener = onItemCheckedListener;
    }

    @Override
    public View getHeaderView(int position, View convertView, ViewGroup parent) {
        LogUtils.i(MsgRepeatAdapter.class, "getHeaderView position= " + position);
        headerViewIndex = position;
        HeaderViewHolder headerViewHolder;
        if (convertView == null) {
            headerViewHolder = new HeaderViewHolder();
            convertView = LayoutInflater.from(mContext).inflate(R.layout.item_contacts_list_header, parent, false);//这种方法不会使布局自适应
            headerViewHolder.textView = (TextView) convertView.findViewById(R.id.header_tv);
            convertView.setTag(headerViewHolder);
        } else {
            headerViewHolder = (HeaderViewHolder) convertView.getTag();
        }
        String firstSpell = mList.get(position).getFirstSpell();
        if (TextUtils.isEmpty(firstSpell) || "|".equalsIgnoreCase(firstSpell)) {
            firstSpell = "#";
        }
        headerViewHolder.textView.setText(String.valueOf(firstSpell.charAt(0)).toUpperCase());
        return convertView;
    }


    @Override
    public long getHeaderId(int position) {
        String firstSpell = mList.get(position).getFirstSpell();
        return TextUtils.isEmpty(firstSpell) ? "#".charAt(0) : firstSpell.charAt(0);
    }


    /**
     * 设置数据
     *
     * @param holder
     * @param position
     */
    @Override
    public void setViewData(ViewHolder holder, final int position) {
        View topLine = holder.findViewById(R.id.top_line);
        final CheckBox selectCb = (CheckBox) holder.findViewById(R.id.select_cb);
        selectCb.setChecked(false);
        ImageView headImgIv = (ImageView) holder.findViewById(R.id.head_img_iv);
        TextView nickNameTv = (TextView) holder.findViewById(R.id.nick_name_tv);
        LinearLayout sexLinear = (LinearLayout) holder.findViewById(R.id.sex_age_ll);
        ImageView sexImgIv = (ImageView) holder.findViewById(R.id.sex_img_iv);
        TextView ageTv = (TextView) holder.findViewById(R.id.age_tv);
        TextView tvTag1 = (TextView) holder.findViewById(R.id.tv_tag1);
        TextView tvTag2 = (TextView) holder.findViewById(R.id.tv_tag2);

        final Contacts contacts = mList.get(position);

        if (position == 0) {
            topLine.setVisibility(View.GONE);
        } else if (!contacts.getFirstSpell().toUpperCase().equals(mList.get(position - 1).getFirstSpell().toUpperCase())) {
            topLine.setVisibility(View.GONE);
        } else {
            topLine.setVisibility(View.VISIBLE);
        }

        selectCb.setChecked(contacts.isChecked());
        holder.getMyView().setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean isChecked = selectCb.isChecked();//获取当前选中状态
                mList.get(position).setChecked(!isChecked);//设置跟当前状态相反的状态
                if (onItemCheckedListener != null) {//将当前状态传回页面
                    onItemCheckedListener.onItemChecked(position, selectCb, !isChecked, contacts);
                }
                notifyDataSetChanged();
            }
        });

        String headUrl = (String) headImgIv.getTag(R.id.img_url);
        if (!TextUtils.isEmpty(ImageUtils.getImgUrl(contacts.getHeadImgUrl())) && !ImageUtils.getImgUrl(contacts.getHeadImgUrl()).equalsIgnoreCase(headUrl)) {
            mGlideUtils.loadCircleImage(ImageUtils.getImgUrl(contacts.getHeadImgUrl()), mContext, headImgIv, R.drawable.default_head_img);
            headImgIv.setTag(R.id.img_url, ImageUtils.getImgUrl(contacts.getHeadImgUrl()));
        }else if(TextUtils.isEmpty(ImageUtils.getImgUrl(contacts.getHeadImgUrl()))){
            mGlideUtils.loadCircleImage(R.drawable.default_head_img, mContext, headImgIv, R.drawable.default_head_img);
            headImgIv.setTag(R.id.img_url,"");
        }

        String nickName = contacts.getNickName();
        //  患者是否有备注，展示样式
        if (!TextUtils.isEmpty(nickName)) {
            if (TextUtils.isEmpty(contacts.getRemark())) {
                //没有备注
                nickNameTv.setText((nickName.length() > 6 ? nickName.substring(0, 6) + "…" : nickName) + "");
            } else {
                //有备注
                nickNameTv.setText(contacts.getRemark() + "(" + (nickName.length() > 3 ? nickName.substring(0, 3) + "…" : nickName) + ")");
            }
        } else {
            nickNameTv.setText("");
        }

        if (TextUtils.isEmpty(contacts.getSex()) && TextUtils.isEmpty(contacts.getAge())) {
            sexLinear.setBackgroundResource(R.color.br_color_white);
        }
        if (!TextUtils.isEmpty(contacts.getSex())) {
            if ("1".equals(contacts.getSex())) {//1为男，其他为女
                sexImgIv.setImageResource(R.drawable.male_icon_white);
                sexLinear.setBackgroundResource(R.drawable.sex_age_blue_bg);
            } else {
                sexImgIv.setImageResource(R.drawable.female_icon_white);
                sexLinear.setBackgroundResource(R.drawable.sex_age_red_bg);
            }
        }
        if (!TextUtils.isEmpty(contacts.getAge()) && !"0".equals(contacts.getAge())) {
            ageTv.setVisibility(View.VISIBLE);
            ageTv.setText(contacts.getAge() + "");
        } else {
            ageTv.setVisibility(View.GONE);
        }

        if (contacts.getTags() != null && contacts.getTags().size() > 0) {
            tvTag1.setText(contacts.getTags().get(0));
            tvTag1.setVisibility(View.VISIBLE);
            if (contacts.getTags().size() > 1) {
                tvTag2.setText(contacts.getTags().get(1));
                tvTag2.setVisibility(View.VISIBLE);
            } else {
                tvTag2.setVisibility(View.GONE);
            }
        } else {
            tvTag1.setVisibility(View.GONE);
            tvTag2.setVisibility(View.GONE);
        }


    }

    private static class HeaderViewHolder {
        TextView textView;
    }

    public interface OnItemCheckedListener {
        void onItemChecked(int position, View view, boolean isChecked, Contacts contacts);
    }
}
