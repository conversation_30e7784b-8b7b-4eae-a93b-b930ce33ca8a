package com.doctor.br.activity;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.doctor.br.activity.mine.PersonalDataActivity;
import com.doctor.br.activity.mine.PrivacyPolicyActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.br.netty.NettyRequestCode;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.client.NettyClient;
import com.doctor.br.netty.model.ResultType;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.util.Calendar;
import java.util.List;


public class LauncherActivity extends NoActionBarActivity {
    public static final int READ_EXTERNAL_STORAGE_REQUEST_CODE = 303;       //请求访问外部存储
    public static final String SHARED_PREFERENCE_NAME = "splashVersion";   //SharedPreference操作的文件
    public static final int NEXT_PAGE_LOGIN_ACTIVITY = 111;   //跳转到登录页面
    public static final int NEXT_PAGE_SPLASH_ACTIVITY = 112;   //跳转到功能引导页页面
    public static final int NEXT_PAGE_MAIN_ACTIVITY = 113;   //跳转到主页面
    public static final int NEXT_PAGE_LOAD_DATA_ACTIVITY = 115;   //跳转到预加载数据页面
    private int mLoginType;//登录类型，1表示密码登录，2表示验证码登录
    private String mLoginUserName;
    private String mLoginPassword;
    private boolean isReturn = false;//是否跳转页面
    private CountDownTimer countDownTimer;
    private TextView tvBanquan;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if ((getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
            finish();
            return;
        }
//        try {
//            Intent intent =getIntent();
//            LogUtils.log("intent_scheme:" +intent.getScheme());
//            Uri uri =intent.getData();
//            LogUtils.log("uri_scheme: "+uri.getScheme());
//            LogUtils.log( "host: "+uri.getHost());
//            LogUtils.log("port: "+uri.getPort());
//            LogUtils.log("path: "+uri.getPath());
//            LogUtils.log( "queryString: "+uri.getQuery());
//            LogUtils.log("queryParameter: "+uri.getQueryParameter("password"));
//        }catch (Exception ignore){}


        setActionBarContentView(R.layout.activity_launcher);
        EventBusUtils.register(this);
        ImageView ivLauncher = (ImageView) findViewById(R.id.iv_launcher);
        tvBanquan = (TextView) findViewById(R.id.tv_banquan);
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        String yearStr = "2021";
        if (year > 2021) {
            yearStr += ("-" + year);
        }
        tvBanquan.setText("©" + yearStr + " Zhongke Qihuang Technology");
        showPrivacyDialog();
    }

    private void postToNextPage() {
        if(getApplication() instanceof AppContext){
            ((AppContext) getApplication()).initSdk();
        }else {
            try {
                AppContext.getInstances().initSdk();
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        initData();
//        checkSelfPermission();
    }

    private static String KEY_IS_AGREE_UA_AND_PP = AppContext.KEY_IS_AGREE_UA_AND_PP;

    private void showPrivacyDialog() {
        boolean isAgree = SharedPreferenceForeverUtils.getBoolean(this, KEY_IS_AGREE_UA_AND_PP, false);
        if (isAgree) {
            postToNextPage();
            return;
        }
        final ConfirmDialog privacyDialog = ConfirmDialog.getInstance(this);
        privacyDialog.setDialogTitle("《隐私政策》与《用户协议》");
        privacyDialog.setNavigationText("不同意");
        privacyDialog.setPositiveText("同意");
        String content = "  欢迎使用必然中医，在您使用必然中医前，请务必认真阅读《隐私政策》与《用户协议》。其中的条款对应用获取权限的信息及用途进行了详细说明，以便您了解应用的安全性可可靠性。";
        SpannableString spannableString = new SpannableString(content);
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(Color.BLACK);
        int pStart = content.indexOf("《隐私政策》");
        int pEnd = pStart + "《隐私政策》".length();

        int uStart = content.indexOf("《用户协议》");
        int uEnd = uStart + "《用户协议》".length();
        ClickableSpan pClickableSpan = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startPrivacyPolicy(LauncherActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_theme));
                ds.setUnderlineText(true);
            }
        };
        ClickableSpan uClickableSpan = new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startUserAgreement(LauncherActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_theme));
                ds.setUnderlineText(true);
            }
        };
        spannableString.setSpan(pClickableSpan, pStart, pEnd, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(uClickableSpan, uStart, uEnd, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        privacyDialog.setDialogContent(spannableString);
        privacyDialog.setCanceledOnTouchOutside(false);
        privacyDialog.setCancelable(false);
        privacyDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
            @Override
            public void onNavigationBtnClicked(View view) {
                if (privacyDialog != null) {
                    privacyDialog.dismiss();
                }
                finish();
            }

            @Override
            public void onPositiveBtnClicked(View view) {
                if (privacyDialog != null) {
                    privacyDialog.dismiss();
                }
                SharedPreferenceForeverUtils.putBoolean(LauncherActivity.this, KEY_IS_AGREE_UA_AND_PP, true);
                postToNextPage();
            }
        });
        privacyDialog.show();

    }

    /**
     * 检测内存卡存储权限
     */
    private void checkSelfPermission() {
        if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            //申请READ_EXTERNAL_STORAGE权限
            requestPermissions(new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    READ_EXTERNAL_STORAGE_REQUEST_CODE,"必然中医需要使用存储权限来初始化使用数据，是否同意使用？");
        }
    }


    /**
     * 初始化数据，判断跳转到哪个界面
     */
    private void initData() {
        mLoginType = SharedPreferenceUtils.getInt(this, PublicParams.LOGIN_TYPE);
        mLoginUserName = SharedPreferenceUtils.getString(this, PublicParams.LOGIN_USERNAME);
        mLoginPassword = SharedPreferenceUtils.getString(this, PublicParams.LOGIN_PASSWORD);
        int splashVersion = getInt(this, "splashVersion");
        if (splashVersion != BaseConfig.SPLASH_VERSION) {
            countDownTimer = new TimeCount(3000, 1000, NEXT_PAGE_SPLASH_ACTIVITY);
            countDownTimer.start();
            Intent intent = new Intent(this, SplashGuidActivity.class);
            startActivity(intent);
            finish();
        } else {
            if (TextUtils.isEmpty(mLoginUserName) || mLoginType == 0 /*|| (mLoginType == 1 && TextUtils.isEmpty(mLoginPassword))*/) {
                //如果从引导页面，调用的initData()方法，并且登录信息不全，应立即跳转到登陆页面；其他登录信息不全，3s后跳转到登陆页面
                countDownTimer = new TimeCount(3000, 1000, NEXT_PAGE_LOGIN_ACTIVITY);
                countDownTimer.start();
            } else {
                //登录信息完整，进行自动登录
                autoLogin();
            }
        }
    }


    /**
     * @param context
     * @param key
     * @return
     * @Description: 获取保存的int数
     */
    public int getInt(Context context, String key) {
        SharedPreferences shared = context.getSharedPreferences(SHARED_PREFERENCE_NAME, Context.MODE_PRIVATE);
        int value = shared.getInt(key, 0);
        return value;
    }

    /**
     * 实现app自动登录
     */
    private void autoLogin() {
        NettyClient mNettyClient = AppContext.getInstances().getNettyClient();
        if (mNettyClient == null) {
            mNettyClient = NettyClient.getInstance(mLoginUserName, mLoginPassword);
            AppContext.getInstances().setNettyClient(mNettyClient);
            countDownTimer = new TimeCount(10000, 1000, NEXT_PAGE_LOGIN_ACTIVITY);
            countDownTimer.start();
        } else {
            countDownTimer = new TimeCount(10000, 1000, NEXT_PAGE_LOGIN_ACTIVITY).start();
            mNettyClient.stop();
        }
        mNettyClient.start();
    }

    /**
     * 自动登录的返回结果
     *
     * @param result 登录结果
     * @sender {@link com.doctor.br.netty.impl.DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN) //在ui线程执行
    public void autoLoginEvent(NettyResult result) {
        if (NettyRequestCode.LOGIN.equalsIgnoreCase(result.getCode())) {//登录返回
            if (ResultType.LOGIN_SUCCESS == result.getResultType()) {
                //登录成功，跳转到MainActivity
                if (!isReturn) {
                    boolean isPerfected = SharedPreferenceUtils.getBoolean(this, PublicParams.USER_IS_PERFECTED);
                    Intent intent;
                    if (isPerfected) {
                        if (isLoadData()) {
                            intent = new Intent(this, LoadDataActivity.class);
                        } else {
                            intent = new Intent(this, MainActivity.class);
                        }
                    } else {
                        intent = new Intent(this, PersonalDataActivity.class);
                    }
                    startActivity(intent);
                    finish();
                    isReturn = true;
                }
            } else {
                String desc = "";
                if (TextUtils.isEmpty(result.getResultDesc())) {
                    desc = "登录失败，请检查您的网络是否正常以及用户名和验证码是否正确";
                }
                if (desc.contains("user not exists")) {
                    desc = "该手机号尚未注册！";
                }
                //登录信息不全，跳转到登陆页面
                if (!isReturn) {
                    Intent intent = new Intent(this, LoginActivity.class);
                    startActivity(intent);
                    finish();
                    isReturn = true;
                }
            }
        }
    }

    /**
     * 判断是否加载数据
     *
     * @return
     */
    private boolean isLoadData() {
        ServerConfigDao serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    return true;
                }

                if (!TextUtils.isEmpty(config.getProduct_drugVersion()) && !config.getProduct_drugVersion().equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
        super.onDestroy();
    }

    /**
     * 倒计时
     */
    class TimeCount extends CountDownTimer {

        private int nextPage;

        /**
         * 倒计时
         *
         * @param millisInFuture    总共倒计时的毫秒数
         * @param countDownInterval 多少毫秒数更新一次
         */
        public TimeCount(long millisInFuture, long countDownInterval, int nextPage) {
            super(millisInFuture, countDownInterval);
            this.nextPage = nextPage;
        }

        @Override
        public void onTick(long millisUntilFinished) {

        }

        @Override
        public void onFinish() {
            if (!isReturn) {//没有返回登录结果
                Intent intent;
                switch (nextPage) {
                    case NEXT_PAGE_LOGIN_ACTIVITY:
                        intent = new Intent(LauncherActivity.this, LoginActivity.class);
                        break;
                    case NEXT_PAGE_SPLASH_ACTIVITY:
                        intent = new Intent(LauncherActivity.this, SplashGuidActivity.class);
                        break;
                    case NEXT_PAGE_MAIN_ACTIVITY:
                        intent = new Intent(LauncherActivity.this, MainActivity.class);
                        break;
                    default:
                        intent = new Intent(LauncherActivity.this, LoginActivity.class);
                        break;
                }
                startActivity(intent);
//                AppContext.getInstances().getNettyClient().setLoginStatusListener(null);
                finish();
                isReturn = true;
            }
        }
    }

}
