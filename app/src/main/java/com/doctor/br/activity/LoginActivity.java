package com.doctor.br.activity;

import android.Manifest;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Color;
import android.os.Bundle;
import android.os.CountDownTimer;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.doctor.br.activity.dialog.UpdateDownloadActivity;
import com.doctor.br.activity.mine.PersonalDataActivity;
import com.doctor.br.activity.mine.PrivacyPolicyActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.CheckUpdateResult;
import com.doctor.br.bean.event.FinishLoginActivityEvent;
import com.doctor.br.db.entity.ServerConfig;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyRequestCode;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.client.NettyClient;
import com.doctor.br.netty.impl.DataReceiverImpl;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.ReLoginCallBack;
import com.doctor.greendao.gen.ServerConfigDao;
import com.doctor.yy.R;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.utils.AppUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.NetUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.LoadingDialog;

import java.util.HashMap;
import java.util.List;

import butterknife.BindView;

public class LoginActivity extends NoActionBarActivity {

    private static final String IS_CKECKED_UA_AND_PP_AGREE = "is_check_ua_pp_agree";
    @BindView(R.id.btn_login_pwd)
    Button btnLoginPwd;
    @BindView(R.id.btn_login_code)
    Button btnLoginCode;
    @BindView(R.id.iv_login_tel)
    ImageView ivLoginTel;
    @BindView(R.id.et_username)
    EditText etUsername;
    @BindView(R.id.iv_clear)
    ImageView ivClear;
    @BindView(R.id.iv_login_pwd)
    ImageView ivLoginPwd;
    @BindView(R.id.et_password)
    EditText etPassword;
    @BindView(R.id.btn_forget_password)
    Button btnForgetPassword;
    @BindView(R.id.iv_login_problem)
    ImageView ivLoginProblem;
    @BindView(R.id.rl_password)
    RelativeLayout rlPassword;
    @BindView(R.id.iv_login_code)
    ImageView ivLoginCode;
    @BindView(R.id.et_verify_code)
    EditText etVerifyCode;
    @BindView(R.id.btn_send_code)
    Button btnSendCode;
    @BindView(R.id.rl_code)
    RelativeLayout rlCode;
    @BindView(R.id.ll_input)
    LinearLayout llInput;
    @BindView(R.id.btn_login)
    Button btnLogin;
    @BindView(R.id.btn_register)
    Button btnRegister;
    @BindView(R.id.btn_debug_model)
    Button btnDebugModel;
    @BindView(R.id.ua_pp)
    TextView uaAndPpTv;
    @BindView(R.id.ua_pp_agree_cb)
    CheckBox uaAndPpAgreeBox;

    public static final int READ_EXTERNAL_STORAGE_REQUEST_CODE = 303;       //请求访问外部存储
    private LoadingDialog loadingDialog;
    private int mLoginType = 1;
    private String mUserName;
    private String mPassword;
    private String mCode;
    private boolean isLogining = false;
    private TimeCount time;
    private int debugCount = 18;//点击18次开启debug模式


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_login);
        setActionBarTitle("登录");
        EventBusUtils.register(this);
        loadingDialog = LoadingDialog.getInstance(this);
        loadingDialog.setTitle("正在登录");
//        checkSelfPermission();
        initView();
        checkUpdate();
    }

    /**
     * 初始化View
     */
    private void initView() {
        time = new TimeCount(60000, 1000);
        registerListener();

        String privacyText = getString(R.string.shu_ming_hao, getString(R.string.privacy_policy_title));
        String termUseText = getString(R.string.shu_ming_hao, getString(R.string.user_agreement_title));
        String content = getString(R.string.privacy_policy_content, privacyText, termUseText);


        int indexPrivacy = content.indexOf(privacyText);
        int indexTermUse = content.indexOf(termUseText);
        SpannableString span = new SpannableString(content);
        span.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startPrivacyPolicy(LoginActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_theme));
                ds.clearShadowLayer();
            }
        }, indexPrivacy, indexPrivacy + privacyText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        span.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startUserAgreement(LoginActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_theme));
                ds.clearShadowLayer();
            }
        }, indexTermUse, indexTermUse + termUseText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        uaAndPpTv.setText(span);
        uaAndPpTv.setMovementMethod(LinkMovementMethod.getInstance());

        String uaAgreeContent = getString(R.string.ua_and_pp_agree, privacyText, termUseText);
        int indexPrivacy2 = uaAgreeContent.indexOf(privacyText);
        int indexTermUse2 = uaAgreeContent.indexOf(termUseText);
        SpannableString span2 = new SpannableString(uaAgreeContent);
        span2.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startPrivacyPolicy(LoginActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_theme));
                ds.clearShadowLayer();
            }
        }, indexPrivacy2, indexPrivacy2 + privacyText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        span2.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                PrivacyPolicyActivity.startUserAgreement(LoginActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                ds.setColor(getResources().getColor(R.color.br_color_theme));
                ds.clearShadowLayer();
            }
        }, indexTermUse2, indexTermUse2 + termUseText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        uaAndPpAgreeBox.setText(span2);
        uaAndPpAgreeBox.setMovementMethod(LinkMovementMethod.getInstance());

        boolean isChecked = SharedPreferenceForeverUtils.getBoolean(this,IS_CKECKED_UA_AND_PP_AGREE,false);
        uaAndPpAgreeBox.setChecked(isChecked);
    }

    /**
     * 注册点击事件
     */
    private void registerListener() {
        AppContext.getInstances().unregisterActivityLifecycleCallbacks(ReLoginCallBack.INSTANCE);
        btnLoginPwd.setOnClickListener(this);
        btnLoginCode.setOnClickListener(this);
        btnForgetPassword.setOnClickListener(this);
        btnSendCode.setOnClickListener(this);
        btnLogin.setOnClickListener(this);
        btnRegister.setOnClickListener(this);
        ivClear.setOnClickListener(this);
        btnDebugModel.setOnClickListener(this);
        etUsername.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!TextUtils.isEmpty(s) && s.length() == 11) {
                    if (!btnSendCode.getText().toString().contains("重新发送")) {
                        btnSendCode.setTextColor(Color.WHITE);
                        btnSendCode.setBackgroundResource(R.drawable.btn_get_code_check_shape);
                        btnSendCode.setClickable(true);
                    }
                    if (mLoginType == 1) {
                        etPassword.requestFocus();
                    } else if (mLoginType == 2) {
                        etVerifyCode.requestFocus();
                    }
                } else {
                    btnSendCode.setClickable(false);
                    btnSendCode.setTextColor(Color.parseColor("#999999"));
                    btnSendCode.setBackgroundResource(R.drawable.btn_get_code_normal_shape);
                }
                if (!TextUtils.isEmpty(s) && s.length() > 0) {
                    ivClear.setVisibility(View.VISIBLE);
                } else {
                    ivClear.setVisibility(View.GONE);
                }
            }
        });
    }

    /**
     * 检测内存卡存储权限
     */
    private void checkSelfPermission() {
        if (ContextCompat.checkSelfPermission(mContext, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            //申请READ_EXTERNAL_STORAGE权限
            requestPermissions(new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                    READ_EXTERNAL_STORAGE_REQUEST_CODE, "必然中医需要使用存储权限来初始化使用数据，是否同意使用？");
        }
    }


    @Override
    public void onClick(View view) {
        super.onClick(view);
        switch (view.getId()) {
            case R.id.btn_login_pwd://密码登录
                mLoginType = 1;
                btnLoginPwd.setTextColor(getResources().getColor(R.color.br_color_white));
                btnLoginPwd.setBackgroundResource(R.drawable.btn_left_circle_blue);
                btnLoginCode.setTextColor(getResources().getColor(R.color.br_color_theme));
                btnLoginCode.setBackgroundResource(R.drawable.btn_right_circle_white);
                rlCode.setVisibility(View.GONE);
                rlPassword.setVisibility(View.VISIBLE);
                break;
            case R.id.btn_login_code://验证码登录
                mLoginType = 2;
                btnLoginCode.setTextColor(getResources().getColor(R.color.br_color_white));
                btnLoginCode.setBackgroundResource(R.drawable.btn_right_circle_blue);
                btnLoginPwd.setTextColor(getResources().getColor(R.color.br_color_theme));
                btnLoginPwd.setBackgroundResource(R.drawable.btn_left_circle_white);
                rlPassword.setVisibility(View.GONE);
                rlCode.setVisibility(View.VISIBLE);
                break;
            case R.id.btn_forget_password://忘记密码
//                Intent intent = new Intent(this, ForgotPasswordActivity.class);
//                startActivity(intent);
                Intent intent1 = new Intent(this, ForgetPasswordActivity.class);
                intent1.putExtra(ForgetPasswordActivity.WITCH_ACTIVITY, LoginActivity.class.getSimpleName());
                startActivity(intent1);
                break;
            case R.id.btn_send_code: {//发送验证码
                mUserName = etUsername.getText().toString().trim();
                if (TextUtils.isEmpty(mUserName) || mUserName.length() != 11) {
                    ToastUtils.showShortMsg(mContext, getString(R.string.toast_login_check_username));
                    return;
                }
                sendVertifyCode(mUserName);
            }
            break;
            case R.id.btn_login://登录
                if (checkInput()) {
                    if (NetUtils.isNetworkAvailable(mContext)) {
                        login();
                    } else {
                        ToastUtils.showShortMsg(mContext, "联网失败，请检查网络设置");
                    }
                }
                break;
            case R.id.btn_register: {
                //注册
                Intent intent = new Intent(this, RegisterActivity.class);
                startActivity(intent);
            }
            break;
            case R.id.iv_clear:
                etUsername.setText("");
                etPassword.setText("");
                etVerifyCode.setText("");
                break;
            case R.id.btn_debug_model:
                debugCount--;
//                if (debugCount > 0 && debugCount <= 9) {
//                    ToastUtils.showShortMsg(this, "还需" + debugCount + "即可打开debug配置模式");
//                }
                if (debugCount == 0) {
//                    ToastUtils.showShortMsg(this, "打开debug配置模式");
                    debugCount = 18;
                    Intent intent = new Intent(this, DebugModelConfigActivity.class);
                    startActivity(intent);
                }
                break;

        }
    }

    /**
     * 检查输入的合法性
     *
     * @return
     */
    private boolean checkInput() {
        boolean isChecked = uaAndPpAgreeBox.isChecked();

        mUserName = etUsername.getText().toString().trim();
        mPassword = etPassword.getText().toString().trim();
        mCode = etVerifyCode.getText().toString().trim();
        if (TextUtils.isEmpty(mUserName) || mUserName.length() != 11) {
            ToastUtils.showShortMsg(mContext, getString(R.string.toast_login_check_username));
            return false;
        }
        if (mLoginType == 1) {
            if (TextUtils.isEmpty(mPassword) || mPassword.length() < 6) {
                ToastUtils.showShortMsg(mContext, getString(R.string.toast_login_check_password));
                return false;
            }
            mPassword = mPassword;
        } else if (mLoginType == 2) {
            if (TextUtils.isEmpty(mCode)) {
                ToastUtils.showShortMsg(mContext, getString(R.string.toast_login_check_vertify_code));
                return false;
            }
            mPassword = mCode;
        }
        if (!isChecked) {
            ToastUtils.showShortMsg(mContext, "请阅读并同意《用户协议》和《隐私政策》");
            ObjectAnimator animator = ObjectAnimator.ofFloat(uaAndPpAgreeBox, "translationX", 0, -30, 0, 30, 0);
            animator.setDuration(250);
            animator.setRepeatCount(2);
            animator.start();
            return false;
        }
        return true;
    }

    /**
     * 发送验证码
     */
    private void sendVertifyCode(String tel) {
        HashMap<String, String> params = new HashMap<String, String>();
        params.put("type", "1");//用户类型
        params.put("tel", tel);
        params.put("optType", "1");
        addHttpPostRequest(HttpUrlManager.GET_CODE, params, ResponseResult.class, this);
    }

    /**
     * 检查App是否有更新
     */
    private void checkUpdate() {
        HashMap<String, String> params = new HashMap<>();
        params.put("systemType", "1");//1表示Android平台，2表示iOS平台
        mRequestTask.addHttpPostRequest(HttpUrlManager.APP_CHECK_UPDATE, params, CheckUpdateResult.class, this);
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        switch (taskId) {
            case HttpUrlManager.GET_CODE:
                if (result.isRequestSuccessed()) {
                    time.start();
                } else {
                    RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.APP_CHECK_UPDATE:
                if (result.isRequestSuccessed()) {
                    CheckUpdateResult checkUpdateResult = (CheckUpdateResult) result.getBodyObject();
                    String local = AppUtils.getVersion(this);
                    if (checkUpdateResult != null && !TextUtils.isEmpty(checkUpdateResult.getVersionNo())) {
                        LogUtils.i(MainActivity.class, "版本号比较：" + local.compareToIgnoreCase(checkUpdateResult.getVersionNo()));
                        if (local.compareToIgnoreCase(checkUpdateResult.getVersionNo()) < 0) {
                            //TODO
                            SharedPreferenceUtils.putBoolean(this, PublicParams.IS_SHOW_UPDATE_DIALOG, false);
                            LogUtils.i(MainActivity.class, "版本需要更新" + local.compareToIgnoreCase(checkUpdateResult.getVersionNo()));
                            Intent intent = new Intent(this, UpdateDownloadActivity.class);
                            intent.putExtra(PublicParams.UPDATE_APP_BEAN, checkUpdateResult);
                            startActivity(intent);
                        }
                    }
                } else {

                }
                break;
        }
    }

    /**
     * 登录操作
     */
    private void login() {
        isLogining = true;

        NettyClient mNettyClient = AppContext.getInstances().getNettyClient();
        if (mNettyClient == null) {
            mNettyClient = NettyClient.getInstance(mUserName, mPassword);
            AppContext.getInstances().setNettyClient(mNettyClient);
        } else {
            mNettyClient.setUsername(mUserName);
            mNettyClient.setPassword(mPassword);
            AppContext.getInstances().setNettyClient(mNettyClient);
        }
        mNettyClient.stop();
        mNettyClient.start();
        loadingDialog.show();
    }


    /**
     * 消息登录返回结果处理方法
     *
     * @param result 返回结果
     * @sender {@link DataReceiverImpl#sendEventBus(NettyResult)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN) //在ui线程执行
    public void onLoginEvent(NettyResult result) {
        if (!isLogining) {
            return;
        }
        if (NettyRequestCode.LOGIN.equalsIgnoreCase(result.getCode())) {
            loadingDialog.dismiss();
            if (NettyResultCode.SUCCESS.equalsIgnoreCase(result.getResultCode())) {
                SharedPreferenceForeverUtils.putBoolean(this,IS_CKECKED_UA_AND_PP_AGREE,true);
//                if (mLoginType == 2) {//验证码登录
                mPassword = null;
                AppContext.getInstances().getNettyClient().setPassword(null);
//                }
                AppContext.getInstances().saveLoginAccount(mLoginType, mUserName, mPassword, true);
                boolean isPerfected = SharedPreferenceUtils.getBoolean(this, PublicParams.USER_IS_PERFECTED);
                Intent intent;
                if (isPerfected) {
                    if (isLoadData()) {
                        intent = new Intent(this, LoadDataActivity.class);
                    } else {
                        intent = new Intent(this, MainActivity.class);
                    }
                } else {
                    intent = new Intent(this, PersonalDataActivity.class);
                }
                startActivity(intent);
                finish();
            } else {
                NettyClient mNettyClient = AppContext.getInstances().getNettyClient();
                if (mNettyClient != null) {
                    mNettyClient.stop();
                }
                String desc = result.getResultDesc();
                if (TextUtils.isEmpty(result.getResultDesc())) {
                    desc = "登录失败，请检查您的网络是否正常以及用户名和验证码是否正确";
                }
                if (desc.contains("user not exists")) {
                    desc = "该手机号尚未注册";
                }
                ToastUtils.showShortMsg(this, desc + "");
            }
            isLogining = false;
        }

    }

    /**
     * 判断是否加载数据
     *
     * @return
     */
    private boolean isLoadData() {
        ServerConfigDao serverConfigDao = AppContext.getInstances().getDaoSession().getServerConfigDao();
        List<ServerConfig> list = serverConfigDao.loadAll();
        if (list != null && list.size() > 0) {
            ServerConfig config = list.get(0);
            if (config != null) {
                if (!TextUtils.isEmpty(config.getArea_addressVersion()) && !config.getArea_addressVersion().equalsIgnoreCase(config.getArea_addressVersionLocal())) {
                    return true;
                }

                if (!TextUtils.isEmpty(config.getProduct_drugVersion()) && !config.getProduct_drugVersion().equalsIgnoreCase(config.getProduct_drugVersionLocal())) {
                    return true;
                }
            }
        }
        String updateContactsListTime = SharedPreferenceUtils.getString(this, PublicParams.UPDATE_CONTACTS_LIST_TIME);
        if (TextUtils.isEmpty(updateContactsListTime)) {
            return true;
        }
        return false;
    }

    class TimeCount extends CountDownTimer {

        public TimeCount(long millisInFuture, long countDownInterval) {
            super(millisInFuture, countDownInterval);
        }

        @Override
        public void onTick(long millisUntilFinished) {
            btnSendCode.setTextColor(Color.parseColor("#999999"));
            btnSendCode.setBackgroundResource(R.drawable.btn_get_code_normal_shape);
            btnSendCode.setClickable(false);
            btnSendCode.setText("重新发送(" + millisUntilFinished / 1000 + "s" + ")");
//            user_tel.setEnabled(false);
//            user_tel.setHint("请于" + millisUntilFinished / 1000 + "秒后重新输入");
        }

        @Override
        public void onFinish() {
            etUsername.setEnabled(true);
            etUsername.requestFocus();
            etUsername.setHint("请输入手机号");
            mUserName = etUsername.getText().toString().trim();
            btnSendCode.setText("获取验证码");
            if (!TextUtils.isEmpty(mUserName) && mUserName.length() == 11) {
                btnSendCode.setClickable(true);
                btnSendCode.setTextColor(Color.WHITE);
                btnSendCode.setBackgroundResource(R.drawable.btn_get_code_check_shape);
            } else {
                btnSendCode.setClickable(false);
                btnSendCode.setTextColor(Color.parseColor("#999999"));
                btnSendCode.setBackgroundResource(R.drawable.btn_get_code_normal_shape);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        debugCount = 18;
    }

    @Override
    public void onBack(Activity activity) {
        super.onBack(activity);
    }

    /**
     * 自动登录成功后，关闭登录界面
     *
     * @send {@link RegisterActivity#closeLoginActivity()}
     * @receive {@link LoginActivity#finishThisActivity(FinishLoginActivityEvent)}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void finishThisActivity(FinishLoginActivityEvent finishLoginActivityEvent) {
        if (finishLoginActivityEvent != null && finishLoginActivityEvent.isFinish()) {
            finish();
        }
    }

    @Override
    protected void onDestroy() {
        EventBusUtils.unRegister(this);
        if (loadingDialog != null) {
            loadingDialog = null;
        }
        if (time != null) {
            time.cancel();
            time = null;
        }
        super.onDestroy();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_BACK:
                activityToBackground();
                break;
            case KeyEvent.KEYCODE_MENU:
                break;
            case KeyEvent.KEYCODE_HOME:
                activityToBackground();
                // 收不到
                break;
            case KeyEvent.KEYCODE_APP_SWITCH:
                // 收不到
                break;
            default:
                break;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 将app在后台运行
     */
    private void activityToBackground() {
        PackageManager pm = getPackageManager();
        ResolveInfo homeInfo = pm.resolveActivity(new Intent(Intent.ACTION_MAIN)
                .addCategory(Intent.CATEGORY_HOME), 0);
        ActivityInfo ai = homeInfo.activityInfo;
        Intent startIntent = new Intent(Intent.ACTION_MAIN);
        startIntent.addCategory(Intent.CATEGORY_LAUNCHER);
        startIntent.setComponent(new ComponentName(ai.packageName,
                ai.name));
        startActivitySafely(startIntent);
    }

    private void startActivitySafely(Intent intent) {
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            startActivity(intent);
        } catch (ActivityNotFoundException e) {
            e.printStackTrace();
        } catch (SecurityException e) {
            e.printStackTrace();
            LogUtils.i(MainActivity.class,
                    "Launcher does not have the permission to launch "
                            + intent
                            + ". Make sure to create a MAIN intent-filter for the corresponding activity "
                            + "or use the exported attribute for this activity.");
        }
    }
}
