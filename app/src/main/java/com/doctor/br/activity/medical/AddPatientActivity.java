package com.doctor.br.activity.medical;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bigkoo.pickerview.TimePickerView;
import com.bigkoo.pickerview.listener.CustomListener;
import com.doctor.br.bean.PopItem;
import com.doctor.br.bean.SavePatientBean;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.utils.DateFormatUtils;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AlertDialog;
import org.newapp.ones.base.widgets.NoEmojiEditText;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Author:sunxiaxia
 * createdDate:2017/10/13
 * description:新增患者
 */
public class AddPatientActivity extends NoActionBarActivity implements RadioGroup.OnCheckedChangeListener {

    @BindView(R.id.patient_name_et)
    NoEmojiEditText mPatientNameEt;
    @BindView(R.id.sex_rg)
    RadioGroup mSexRg;
    @BindView(R.id.male_rb)
    RadioButton maleRb;
    @BindView(R.id.female_rb)
    RadioButton femaleRb;
    @BindView(R.id.pregnant_rg)
    RadioGroup mPregnantRg;
    @BindView(R.id.is_pregnant_rl)
    RelativeLayout mIsPregnantRl;
    @BindView(R.id.no_pregnant_rb)
    RadioButton noPregnantRb;
    @BindView(R.id.pregnant_rb)
    RadioButton pregnantRb;
    @BindView(R.id.birthday_tv)
    TextView mBirthdayTv;
    @BindView(R.id.btn_cancel)
    Button mBtnCancel;
    @BindView(R.id.btn_save)
    Button mBtnSave;
    @BindView(R.id.dialog_title)
    TextView mTitle;
    private TimePickerView mPickerView;
    private String sexStr = "0";
    private String isPregnant = "0";
    private RequestCallBack mRequestCallBack;
    private String name;
    private String birthday;
    private String mAge;
    private String patientId;
    private boolean isEdit;//是否编辑状态
    private String takerId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        overridePendingTransition(R.anim.push_bottom_in, R.anim.push_bottom_out);
        setActionBarContentView(R.layout.activity_add_patient);
        PopItem patientInfo = (PopItem) getIntent().getSerializableExtra("patientInfo");
        isEdit = getIntent().getBooleanExtra("isEdit",false);
        if (isEdit){
            mTitle.setText("编辑患者");
        }else {
            mTitle.setText("新增患者");
        }
        if (patientInfo != null) {
            takerId = patientInfo.getId();
            mPatientNameEt.setText(TextUtils.isEmpty(patientInfo.getName()) ? "" : patientInfo.getName());
            if (!TextUtils.isEmpty(patientInfo.getSex())) {//1 男  2女  0未知
                 if ("1".equals(patientInfo.getSex())) {
                    maleRb.setChecked(true);
                    mIsPregnantRl.setVisibility(View.GONE);
                } else {
                    femaleRb.setChecked(true);
                    mIsPregnantRl.setVisibility(View.VISIBLE);
                }
            }
            if (!TextUtils.isEmpty(patientInfo.getIsPregnant())) {
                if ("0".equals(patientInfo.getIsPregnant())) {
                    noPregnantRb.setChecked(true);
                } else if ("1".equals(patientInfo.getIsPregnant())) {
                    pregnantRb.setChecked(true);
                }
            }
            mBirthdayTv.setText(TextUtils.isEmpty(patientInfo.getBirthday()) ? "请点击选择" : patientInfo.getBirthday());
        }


        setViewListener();

    }

    private void setViewListener() {
//        findViewById(R.id.container).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                onBackPressed();
//            }
//        });
        mSexRg.setOnCheckedChangeListener(this);
        mPregnantRg.setOnCheckedChangeListener(this);
    }

    @OnClick({R.id.container, R.id.dialog_container, R.id.birthday_tv, R.id.btn_cancel, R.id.btn_save})
    public void onViewClicked(View view) {
        name = mPatientNameEt.getText().toString().trim();
        if ("请点击选择".equals(mBirthdayTv.getText().toString().trim())) {
            birthday = "";
        } else {
            birthday = mBirthdayTv.getText().toString().trim();
        }

        switch (view.getId()) {
            case R.id.container:
                onBackPressed();
                break;
            case R.id.dialog_container:
                break;
            case R.id.birthday_tv:
                showTimePickerView();
                break;
            case R.id.btn_cancel:
                AddPatientActivity.this.finish();
                overridePendingTransition(R.anim.push_bottom_in, R.anim.push_bottom_out);
                break;
            case R.id.btn_save:
                if (TextUtils.isEmpty(name)) {
                    ToastUtils.showShortMsg(mContext, "请输入患者姓名！");
                } else if (!QuicklyPrescriptionActivity.isChineseName(name)) {
                    String text = "根据《处方管理法》规定，开方时需要填写真实姓名，请跟患者确认真实姓名。";
                    final AlertDialog dialog = AlertDialog.getInstance(this);
                    dialog.setDialogTitle("")
                            .setDialogContent(text)
                            .setPositiveText("确定")
                            .setOnPositiveBtnClickedListener(views -> {
                                dialog.dismiss();
                            });
                    dialog.show();
                } else if (TextUtils.isEmpty(birthday)) {
                    ToastUtils.showShortMsg(mContext, "请选择出生年月！");
                } else {
                    if (TextUtils.isEmpty(mAge)){
                        mAge = DateFormatUtils.getAgeByBirthDay(mBirthdayTv.getText().toString());
                    }
                    savePatientMsg(name, sexStr, isPregnant, birthday);
                }


                break;
        }
    }

    private void savePatientMsg(String name, String sex, String isPregnant, String brithday) {
        HashMap map = new HashMap();
        if (isEdit){
            map.put("userId",SharedPreferenceUtils.getString(mContext,PublicParams.USER_ID));
            map.put("takerId",takerId);
            map.put("takerName",name);
            map.put("takerSex",sex);
            map.put("takerIsPregnant",isPregnant);
            map.put("takerBirthday",birthday);
            map.put("patientId",SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_ID));
            mRequestCallBack = addHttpPostRequest(HttpUrlManager.UPDATE_TAKER_PATIENT, map, SavePatientBean.class, this);
        }else {
            map.put("patientId", SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_ID));
            map.put("name", name);
            map.put("sex", sex);
            map.put("isPregnant", isPregnant);
            map.put("brithday", brithday);
            mRequestCallBack = addHttpPostRequest(HttpUrlManager.ADD_PATIENT, map, SavePatientBean.class, this);
        }
    }

    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (mLoadingDialog != null) {
            mLoadingDialog.dismiss();
        }
        switch (taskId) {
            case HttpUrlManager.ADD_PATIENT:
            case HttpUrlManager.UPDATE_TAKER_PATIENT:
                if (result != null && result.isRequestSuccessed()) {
                    SavePatientBean patientBean = (SavePatientBean) result.getBodyObject();
                    if (patientBean != null) {
                        patientId = TextUtils.isEmpty(patientBean.getUserId()) ? "" : patientBean.getUserId();
                    }
                    Intent intent = new Intent();
                    intent.putExtra("name", name);
                    intent.putExtra("age", mAge + "");
                    intent.putExtra("sex", sexStr);
                    intent.putExtra("isPregnant", isPregnant);
                    intent.putExtra("patientId", patientId);
                    intent.putExtra("birthday",birthday);
                    setResult(RESULT_OK, intent);
                    AddPatientActivity.this.finish();
                    overridePendingTransition(R.anim.push_bottom_in, R.anim.push_bottom_out);
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }

                break;
        }
    }

    /**
     * 显示日期选择器
     */
    private void showTimePickerView() {
        hideKeyBoard(mPatientNameEt);
        String default_date = "1980-01-01";
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {

            Date date = format.parse(default_date);
            Calendar calendar = Calendar.getInstance();
            final int currentYear = calendar.get(Calendar.YEAR);
            Calendar startCalendar = Calendar.getInstance();
            Date startDate = format.parse(currentYear - 150 + "-01-01");
            startCalendar.setTime(startDate);
            calendar.setTime(date);
            if (mPickerView == null) {
                mPickerView = new TimePickerView.Builder(this, new TimePickerView.OnTimeSelectListener() {
                    @Override
                    public void onTimeSelect(Date date, View v) {
                        int month = date.getMonth() + 1;
                        int day = date.getDate();
                        int year = date.getYear() + 1900;
//                        mAge = currentYear - year;
                        String monthStr = "";
                        String dayStr = "";
                        if (month < 10) {
                            monthStr = "0" + month;
                        } else {
                            monthStr = "" + month;
                        }
                        if (day < 10) {
                            dayStr = "0" + day;
                        } else {
                            dayStr = "" + day;
                        }
                        mBirthdayTv.setText(year + "-" + monthStr + "-" + dayStr);
                        mAge = DateFormatUtils.getAgeByBirthDay(date);
                        mPickerView.dismiss();

                    }
                })
                        .setLayoutRes(R.layout.pickerview_custom_time, new CustomListener() {

                            @Override
                            public void customLayout(View v) {
                                final TextView tvSubmit = (TextView) v.findViewById(R.id.tv_finish);
                                TextView tvCancel = (TextView) v.findViewById(R.id.tv_cancel);
                                v.findViewById(R.id.title_rl).setOnTouchListener(new View.OnTouchListener() {
                                    @Override
                                    public boolean onTouch(View v, MotionEvent event) {
                                        return true;
                                    }
                                });
                                v.findViewById(R.id.unit_ll).setOnTouchListener(new View.OnTouchListener() {
                                    @Override
                                    public boolean onTouch(View v, MotionEvent event) {
                                        return true;
                                    }
                                });
                                tvSubmit.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        mPickerView.returnData();
                                        mPickerView.dismiss();
                                    }
                                });
                                tvCancel.setOnClickListener(new View.OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        mPickerView.dismiss();
                                    }
                                });
                            }
                        })
                        .setLineSpacingMultiplier(1.8f)
                        .setType(new boolean[]{true, true, true, false, false, false})
                        .setLabel("", "", "", "", "", "")
                        .isCenterLabel(false)
                        .setContentSize(15)
//                        .setRange(currentYear - 150, currentYear)
                        .setRangDate(startCalendar, Calendar.getInstance())
                        .gravity(Gravity.CENTER).build();
            }

            mPickerView.setDate(calendar);
            mPickerView.show();
        } catch (ParseException e) {
            e.printStackTrace();
        }

    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (group.getId()) {

            case R.id.sex_rg:
                RadioButton rb = (RadioButton) group.findViewById(checkedId);
                if (!TextUtils.isEmpty((String) rb.getTag())) {
                    sexStr = (String) rb.getTag();
                }
                if (checkedId == R.id.female_rb) {
                    mIsPregnantRl.setVisibility(View.VISIBLE);
                } else {
                    mIsPregnantRl.setVisibility(View.GONE);
                }
                break;
            case R.id.pregnant_rg:
                RadioButton rb1 = (RadioButton) group.findViewById(checkedId);
                if (!TextUtils.isEmpty((String) rb1.getTag())) {
                    isPregnant = (String) rb1.getTag();
                }
                break;
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        overridePendingTransition(R.anim.push_bottom_in, R.anim.push_bottom_out);
    }
}
