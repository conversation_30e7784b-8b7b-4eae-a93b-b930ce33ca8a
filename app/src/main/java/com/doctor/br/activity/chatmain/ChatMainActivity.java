package com.doctor.br.activity.chatmain;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.doctor.br.activity.medical.AddMedicineActivity;
import com.doctor.br.activity.mine.QualificationActivity;
import com.doctor.br.adapter.chatmain.ChatMainAdapter;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.AuthState;
import com.doctor.br.bean.AuthStateResult;
import com.doctor.br.bean.DataCacheType;
import com.doctor.br.bean.PatientExpandInfoBean;
import com.doctor.br.bean.PopItem;
import com.doctor.br.bean.event.PatientInfoChangeEvent;
import com.doctor.br.bean.medical.OrderMsgBean;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.DataCache;
import com.doctor.br.db.entity.Session;
import com.doctor.br.db.utils.DataCacheDaoUtil;
import com.doctor.br.fragment.chatmain.ChatFragment;
import com.doctor.br.fragment.chatmain.DossierFragment;
import com.doctor.br.fragment.chatmain.MedicationFragment;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyMsgNotify;
import com.doctor.br.netty.NettyRequestCode;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.NettyResultCode;
import com.doctor.br.netty.NotifyType;
import com.doctor.br.netty.model.Message;
import com.doctor.br.netty.model.PatientMsg;
import com.doctor.br.utils.BroadcastAction;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.utils.SelectImgUtils;
import com.doctor.br.view.NoScrollViewPager;
import com.doctor.br.view.TabView;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.yy.R;
import com.doctor.yy.R2;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.activity.NoActionBarActivity;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AlertDialog;
import org.newapp.ones.base.widgets.ConfirmDialog;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import butterknife.OnClick;

public class ChatMainActivity extends NoActionBarActivity implements ViewPager.OnPageChangeListener {
    public static String POSITION = "position";
    public static String ISREPLACE = "isReplace";
    public static String TAKER_INFO = "takerInfo";

    @BindView(R2.id.tab_group)
    RadioGroup tabGroup;
    @BindView(R2.id.viewpager_main)
    NoScrollViewPager viewpagerMain;
    @BindView(R2.id.tab_dossier)
    TabView tabDossier;
    @BindView(R2.id.tab_chat)
    TabView tabChat;
    @BindView(R2.id.tab_medication)
    TabView tabMedication;
    @BindView(R2.id.patient_name_tv)
    TextView patientNameTv;
    @BindView(R2.id.sex_img_iv)
    ImageView sexImgIv;
    @BindView(R2.id.age_tv)
    TextView ageTv;
    @BindView(R2.id.sex_age_ll)
    LinearLayout sexAgeLl;
    @BindView(R2.id.call_img)
    ImageView callImg;
    private ChatMainAdapter mViewPagerAdapter;
    private List<Fragment> mFragmentList;
    private List<String> mTabTitleList;
    private BroadcastReceiver scrollChangeReceiver;
    private boolean isRecording = false; //判断是否正在录音
    private boolean isScrolled = true; //判断是否可以滑动
    private ConfirmDialog mConfirmDialog;
    private String patientUserName = "";
    private String mPhone = "";
    private String patientSex = "";
    private String patientAge = "";
    private DossierFragment dossierFragment;
    private AuthStateResult authStateResult;
    private int requestCount = 0;
    //private String patientId;
    private ConfirmDialog authConfirmDialog;
    private AlertDialog authAlertDialog;
    private ChatFragment chatFragment;
    private MedicationFragment medicationFragment;
    private ConfirmDialog backConfirmDialog;
    private int currentPosition;
    private DataCacheDaoUtil dataCacheUtil;
    private boolean isSaveMedicineMsg = true;//是否保存用药信息，默认保存，当点击退出后就不保存用药信息
    private DataCache mCachePresMsg;
    private String userId, patientId;//患者本人id;
    private OrderMsgBean medicationParams;
    private boolean isToAddMedicine = true;//是否跳转到添加药材界面，当首次进入交流主界面时为true，否则为false
    private ContactsDao mContactsDao;
    private Contacts mContacts;
    private String fromPage;
    private boolean hasShowMedicationFg;//是否已经显示了用药界面
    private RequestCallBack getPatientExpandInfoCallback;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_chat_main);
        EventBusUtils.register(this);
        patientUserName = getIntent().getStringExtra(PublicParams.PATIENT_USRE_NNAME);
        mPhone = getIntent().getStringExtra(PublicParams.PATIENT_USRE_MOBILE);
        patientAge = getIntent().getStringExtra(PublicParams.PATIENT_USRE_AGE);
        patientSex = getIntent().getStringExtra(PublicParams.PATIENT_USRE_SEX);
        currentPosition = getIntent().getIntExtra(POSITION, 1);
        fromPage = getIntent().getStringExtra(PublicParams.TO_MEDICATION_FROM_PAGE);
        mContactsDao = AppContext.getInstances().getDaoSession().getContactsDao();
        String patientId = SharedPreferenceUtils.getString(this, PublicParams.PATIENT_USRE_ID);
        String userId = SharedPreferenceUtils.getString(this, PublicParams.USER_ID);
        mContacts = mContactsDao.load(patientId + "_" + userId);
        initTitleMsg(mContacts);//初始化标题信息
        initView();
        getPatientExpandInfo();
        initReceiver();
    }

    /**
     * 初始化标题信息
     *
     * @param contacts
     */
    private void initTitleMsg(Contacts contacts) {
        if (contacts != null) {
            if (!TextUtils.isEmpty(contacts.getNickName())) {
                patientUserName = contacts.getNickName();
            }
            if (!TextUtils.isEmpty(contacts.getMobile())) {
                mPhone = contacts.getMobile();
            }

            if (!TextUtils.isEmpty(contacts.getSex())) {
                patientSex = contacts.getSex();
            }
            if (!TextUtils.isEmpty(contacts.getAge())) {
                patientAge = contacts.getAge();
            }
        }
        if (patientUserName != null) {
            patientNameTv.setText(patientUserName.length() > 10 ? patientUserName.substring(0, 10) + "…" : patientUserName);
        }
        if (!TextUtils.isEmpty(mPhone)) {
            callImg.setVisibility(View.VISIBLE);
        } else {
            callImg.setVisibility(View.GONE);
        }
        if ("1".equals(patientSex)) {//1为男，其他为女
            sexImgIv.setImageResource(R.drawable.male_icon_white);
            sexAgeLl.setBackgroundResource(R.drawable.sex_age_blue_bg);
        } else {
            sexImgIv.setImageResource(R.drawable.female_icon_white);
            sexAgeLl.setBackgroundResource(R.drawable.sex_age_red_bg);
        }
        if (!TextUtils.isEmpty(patientAge) && !"0".equals(patientAge)) {
            ageTv.setVisibility(View.VISIBLE);
            ageTv.setText(patientAge);
        } else {
            ageTv.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(patientAge) && TextUtils.isEmpty(patientSex)) {
            sexAgeLl.setBackgroundResource(R.color.br_color_white);
        }
    }

    /**
     * 医生在PatientInfoActivity页面修改患者备注
     */
    @Subscribe
    public void changeRemark(PatientInfoChangeEvent patientInfoChangeEvent) {
        if (patientInfoChangeEvent != null && patientInfoChangeEvent.getEventType() == PatientInfoChangeEvent.CHANGE_INFO) {
            mContacts = mContactsDao.load(patientId + "_" + userId);
            initTitleMsg(mContacts);//初始化标题信息
        }
    }

    /**
     * 微信患者信息更改 eventbus 事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onWechatUserUpdateEvent(NettyResult nettyResult) {
        if ("0008".equals(nettyResult.getCode()) && nettyResult.getResultCode() == null) {
            // 更新微信用户信息
            Message message = nettyResult.getMessage();
            PatientMsg patientMsg =  message.getContent().getUserInfo();
            updatePatientInfo(patientMsg);
        }
    }

    /**
     * 更新用户头部信息
     */
    /**
     * 更新患者信息
     * @param patientMsg 患者信息对象
     */
    private void updatePatientInfo(PatientMsg patientMsg) {
        if (patientMsg == null) {
            return;
        }

        // 更新姓名
        if (!TextUtils.isEmpty(patientMsg.getName())) {
            patientUserName = patientMsg.getName();
            patientNameTv.setText(patientUserName.length() > 10 ?
                    patientUserName.substring(0, 10) + "…" : patientUserName);
        }

        // 更新手机号
        if (!TextUtils.isEmpty(patientMsg.getMobile())) {
            mPhone = patientMsg.getMobile();
            callImg.setVisibility(View.VISIBLE);
        } else {
            callImg.setVisibility(View.GONE);
        }

        // 更新性别
        if (!TextUtils.isEmpty(patientMsg.getSex())) {
            patientSex = patientMsg.getSex();
            if ("1".equals(patientSex)) {  // 1为男，其他为女
                sexImgIv.setImageResource(R.drawable.male_icon_white);
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_blue_bg);
            } else {
                sexImgIv.setImageResource(R.drawable.female_icon_white);
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_red_bg);
            }
        }

        // 更新年龄
        if (!TextUtils.isEmpty(patientMsg.getAge())) {
            patientAge = patientMsg.getAge();
            if (!"0".equals(patientAge)) {
                ageTv.setVisibility(View.VISIBLE);
                ageTv.setText(patientAge);
            } else {
                ageTv.setVisibility(View.GONE);
            }
        } else {
            ageTv.setVisibility(View.GONE);
        }

        // 如果没有性别和年龄信息则使用白色背景
        if (TextUtils.isEmpty(patientAge) && TextUtils.isEmpty(patientSex)) {
            sexAgeLl.setBackgroundResource(R.color.br_color_white);
        }

        // 更新 Contacts 数据库
        if (mContactsDao != null && !TextUtils.isEmpty(patientMsg.getUserId())) {
            String contactId = patientMsg.getUserId() + "_" + userId;
            Contacts contact = mContactsDao.load(contactId);
            if (contact != null) {
                contact.setNickName(patientUserName);
                contact.setMobile(mPhone);
                contact.setSex(patientSex);
                contact.setAge(patientAge);
                mContactsDao.update(contact);
            }
        }
    }

    /**
     * 初始化控件
     */

    private void initView() {
        viewpagerMain.setOffscreenPageLimit(3);
        viewpagerMain.addOnPageChangeListener(this);
        mFragmentList = new ArrayList<Fragment>();
        dossierFragment = new DossierFragment();
        chatFragment = new ChatFragment();
        medicationFragment = new MedicationFragment();
        dossierFragment.setParams(patientUserName);
        mFragmentList.add(dossierFragment);
        mFragmentList.add(chatFragment);
        mFragmentList.add(medicationFragment);
        mViewPagerAdapter = new ChatMainAdapter(getSupportFragmentManager(), this, mFragmentList);
        viewpagerMain.setAdapter(mViewPagerAdapter);
//        ((TabView) tabGroup.getChildAt(1)).select();
        viewpagerMain.setCurrentItem(currentPosition);

        mConfirmDialog = ConfirmDialog.getInstance(this);
        mConfirmDialog.setPositiveText("立即拨号")
                .setNavigationText("取消")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        mConfirmDialog.dismiss();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        Intent intent = new Intent(Intent.ACTION_DIAL, Uri.parse("tel:" + mPhone));
                        startActivity(intent);
                        mConfirmDialog.dismiss();
                    }
                });
        authAlertDialog = AlertDialog.getInstance(mContext);
        authConfirmDialog = ConfirmDialog.getInstance(mContext);
//        authAlertDialog.setCanceledOnTouchOutside(false);
//        authConfirmDialog.setCanceledOnTouchOutside(false);
//        authAlertDialog.setCancelable(false);
//        authConfirmDialog.setCancelable(false);
        patientId = SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_ID);
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        //获取缓存的用药信息
        mCachePresMsg = DataCacheDaoUtil.getInstance()
                .select(DataCacheType.CACHE_PRESCRIPTION_MSG, userId + "_" + patientId);
        if (mCachePresMsg != null) {
            medicationParams = JSON.parseObject(mCachePresMsg.getValue(), OrderMsgBean.class);
            System.out.println("聊天主界面获取缓存的药方信息*********" + patientUserName + ":" + patientId + "==============" + JSON.toJSONString(mCachePresMsg));
        }
        LogUtils.i(ChatMainActivity.class, "****ChatMainActivity****initView*******");

        //处理从问诊单、复诊单跳用药界面时，先判断看是否有缓存的药材，有的话去添加药材界面，没有则到用药界面
        if (currentPosition == 2 && !TextUtils.isEmpty(fromPage) && PublicParams.FROM_WZD_OR_FZD.equals(fromPage)) {
            if (medicationParams != null) {
                if (medicationParams.getPreDetailList() != null
                        && medicationParams.getPreDetailList().size() > 0 && isToAddMedicine) {
                    toAddMedicineActivity();
                } else {
                    viewpagerMain.setCurrentItem(currentPosition);
                }
            } else {
                viewpagerMain.setCurrentItem(currentPosition);
            }
        } else {
            viewpagerMain.setCurrentItem(currentPosition);
        }
    }

    /**
     * 设置当前的页面
     *
     * @param currentPosition
     */
    private void setCurrentPage(int currentPosition) {
        this.currentPosition = currentPosition;
        viewpagerMain.setCurrentItem(currentPosition);
    }


    @Override
    public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

    }

    @Override
    public void onPageSelected(int position) {
        ((TabView) tabGroup.getChildAt(position)).select();
        if (dossierFragment != null && position == 0) {
            dossierFragment.onDossierFragmentSelect();
        } else if (position == 2 && isToAddMedicine) {
            hasShowMedicationFg = true;
            if (medicationParams != null) {
                if (medicationParams.getPreDetailList() != null
                        && medicationParams.getPreDetailList().size() > 0) {
                    toAddMedicineActivity();
                }
            }
        }
//        if (position != 1) {
//            showAuthDialog();
//        }
    }

    /**
     * 跳转到添加药材界面
     */
    private void toAddMedicineActivity() {
        Intent intent = new Intent(mContext, AddMedicineActivity.class);
        intent.putExtra(PublicParams.DOSAGE_SUPPLIER_BEAN, medicationParams.getFormDetailMsg());
        intent.putExtra(PublicParams.DOSAGEFORMBEAN, medicationParams.getFormList());
        intent.putExtra(PublicParams.IS_PREGNANT, medicationParams.getTakerIsPregnant());
        intent.putExtra("leftPosition", medicationParams.getLeftP());
        intent.putExtra("rightPosition", medicationParams.getRightP());
        intent.putExtra("editMedicines", (Serializable) medicationParams.getPreDetailList());
        intent.putExtra(PublicParams.IS_CACHE_MEDICINE, true);
        startActivity(intent);
        isToAddMedicine = false;
    }


    @Override
    public void onPageScrollStateChanged(int state) {

    }

    /**
     * 显示认证相关的弹窗
     */
    private void showAuthDialog() {
        if (authStateResult == null) {
            ToastUtils.showShortMsg(this, "请求失败，请稍候重试。");
            setCurrentPage(1);
            return;
        }
        if (!AuthState.AUTH_STATE_SUCCESSFUL.equalsIgnoreCase(authStateResult.getIsAuthentication())) {  //如果没有认证成功过，则需要弹窗提示
            String sockDay = authStateResult.getStockDay();
            if (TextUtils.isEmpty(sockDay)) {
                ToastUtils.showShortMsg(this, "请求失败，请稍候重试。");
                return;
            }
            if (AuthState.AUTH_STATE_NO_AUTH.equalsIgnoreCase(authStateResult.getCurrentAuthenticationState())) {//未审核
                noAuthDialog(authStateResult.getCurrentAuthenticationState(), sockDay);
            } else if (AuthState.AUTH_STATE_IN_REVIEW.equalsIgnoreCase(authStateResult.getCurrentAuthenticationState())) {//审核中
                inReviewDialog(sockDay);
            } else if (AuthState.AUTH_STATE_FAILED.equalsIgnoreCase(authStateResult.getCurrentAuthenticationState())) {//审核失败
                authFailedDialog(authStateResult.getCurrentAuthenticationState(), sockDay);
            }
        }
    }

    /**
     * 审核失败状态的显示逻辑
     *
     * @param authState 认证状态
     * @param sockDay   试用期剩余天数
     */
    private void authFailedDialog(final String authState, String sockDay) {
        if ("0".equalsIgnoreCase(sockDay)) {//试用期结束
            authConfirmDialog.setDialogContent("审核失败，请重新认证。")
                    .setPositiveText("前往认证")
                    .setNavigationText("暂不认证")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            setCurrentPage(1);
                            authConfirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            setCurrentPage(1);
                            authConfirmDialog.dismiss();
                            //todo
                            Intent intent = new Intent(mContext, QualificationActivity.class);
                            intent.putExtra(QualificationActivity.STATE, authState);
                            startActivity(intent);
                        }
                    }).show();
        } else {
            //改变字体颜色
            //先构造SpannableString
            SpannableStringBuilder spanString = new SpannableStringBuilder("试用期还剩");
            //再构造一个改变字体颜色的Span
            ForegroundColorSpan span = new ForegroundColorSpan(Color.RED);
            int start = spanString.length();
            //将这个Span应用于指定范围的字体
            spanString.append(sockDay);
            spanString.setSpan(span, start, spanString.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            spanString.append("天，请尽快认证。");
            //设置给EditText显示出来
            authConfirmDialog.setDialogContent(spanString)
                    .setPositiveText("前往认证")
                    .setNavigationText("暂不认证")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
//                            viewpagerMain.setCurrentItem(1);
                            authConfirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            setCurrentPage(1);
                            authConfirmDialog.dismiss();
                            //todo
                            Intent intent = new Intent(mContext, QualificationActivity.class);
                            intent.putExtra(QualificationActivity.STATE, authState);
                            startActivity(intent);
                        }
                    }).show();
        }
    }

    /**
     * 审核中状态的显示逻辑
     *
     * @param sockDay
     */
    private void inReviewDialog(String sockDay) {
        if ("0".equalsIgnoreCase(sockDay)) {//试用期结束
            authAlertDialog.setDialogContent("认证审核中...")
                    .setPositiveText("确认")
                    .setOnPositiveBtnClickedListener(new AlertDialog.OnPositiveBtnClickedListener() {
                        @Override
                        public void onPositiveBtnClicked(View view) {
                            setCurrentPage(1);
                            authAlertDialog.dismiss();
                        }
                    }).show();
        } else {

        }
    }

    /**
     * 未认证状态的显示逻辑
     *
     * @param authState
     * @param sockDay
     */
    private void noAuthDialog(final String authState, String sockDay) {
        if ("0".equalsIgnoreCase(sockDay)) {//试用期结束
            authConfirmDialog.setDialogContent("认证通过后可使用该功能")
                    .setPositiveText("前往认证")
                    .setNavigationText("暂不认证")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
                            setCurrentPage(1);
                            authConfirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            setCurrentPage(1);
                            authConfirmDialog.dismiss();
                            //todo
                            Intent intent = new Intent(mContext, QualificationActivity.class);
                            intent.putExtra(QualificationActivity.STATE, authState);
                            startActivity(intent);
                        }
                    }).show();
        } else {
            //改变字体颜色
            //先构造SpannableString
            SpannableStringBuilder spanString = new SpannableStringBuilder("试用期还剩");
            //再构造一个改变字体颜色的Span
            ForegroundColorSpan span = new ForegroundColorSpan(Color.RED);
            int start = spanString.length();
            //将这个Span应用于指定范围的字体
            spanString.append(sockDay);
            spanString.setSpan(span, start, spanString.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            spanString.append("天，请尽快认证。");
            //设置给EditText显示出来
            authConfirmDialog.setDialogContent(spanString)
                    .setPositiveText("前往认证")
                    .setNavigationText("暂不认证")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                        @Override
                        public void onNavigationBtnClicked(View view) {
//                            viewpagerMain.setCurrentItem(1);
                            authConfirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            setCurrentPage(1);
                            authConfirmDialog.dismiss();
                            //todo
                            Intent intent = new Intent(mContext, QualificationActivity.class);
                            intent.putExtra(QualificationActivity.STATE, authState);
                            startActivity(intent);
                        }
                    }).show();
        }
    }

    /**
     * 初始化广播
     */
    private void initReceiver() {
        scrollChangeReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (BroadcastAction.RECORDS_BUTTON_LONG_CLICK_STATE_CHANGED.equals(intent.getAction())) {
                    isRecording = intent.getBooleanExtra("isRecording", false);
                    if (isRecording) {
                        viewpagerMain.setNoScroll(isRecording);
                    } else {
                        viewpagerMain.setNoScroll(isRecording && isScrolled);
                    }
                } else if (BroadcastAction.SET_CHAT_MAIN_POSITION.equals(intent.getAction())) {
                    int position = intent.getIntExtra(POSITION, 1);
                    boolean isReplace = intent.getBooleanExtra(ISREPLACE, false);

                    PopItem popItem = (PopItem) intent.getSerializableExtra(TAKER_INFO);
                    if (popItem != null && medicationFragment != null) {
                        medicationFragment.checkIsClearData(popItem);
                    }
                    if (isReplace) {
                        try {
//                            //清空缓存的药方信息(成功发送了订单后、从缓存中进入添加药材界面点击返回——》取消后需要做此操作)
                            DataCacheDaoUtil.getInstance().clearCache(DataCacheType.CACHE_PRESCRIPTION_MSG
                                    , userId + "_" + patientId);
                            //当订单成功发送给患者后缓存清空了，所以这里需要更新缓存信息
                            mCachePresMsg = DataCacheDaoUtil.getInstance()
                                    .select(DataCacheType.CACHE_PRESCRIPTION_MSG, userId + "_" + patientId);
                            if (mCachePresMsg != null) {
                                medicationParams = JSON.parseObject(mCachePresMsg.getValue(), OrderMsgBean.class);
                            } else {
                                medicationParams = new OrderMsgBean();
                            }
                            if (medicationFragment != null) {
                                medicationFragment.clearAllData();//用药界面恢复默认界面
                                medicationFragment.getMedicalServiceFeeRadio();//重新获取医技服务比例
                                medicationFragment.initDefaultPatientInfo();
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    setCurrentPage(position);
                }
            }
        };

        IntentFilter filter = new IntentFilter(BroadcastAction.RECORDS_BUTTON_LONG_CLICK_STATE_CHANGED);
        filter.addAction(BroadcastAction.SET_CHAT_MAIN_POSITION);
        registerReceiver(scrollChangeReceiver, filter);
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        SelectImgUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults);
        if (chatFragment != null) {
            chatFragment.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    /**
     * @param view
     */
    @OnClick({R.id.tab_dossier, R.id.tab_chat, R.id.tab_medication, R.id.back_img, R.id.call_img})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tab_dossier:
                setCurrentPage(0);
                break;
            case R.id.tab_chat:
                setCurrentPage(1);
                break;
            case R.id.tab_medication:
                hasShowMedicationFg = true;
                if (currentPosition != 2) {
                    if (medicationParams != null) {
                        if (medicationParams.getPreDetailList() != null
                                && medicationParams.getPreDetailList().size() > 0 && isToAddMedicine) {
                            toAddMedicineActivity();
                        } else {
                            setCurrentPage(2);
                        }
                    } else {
                        setCurrentPage(2);
                    }
                }
                break;
            case R.id.back_img:
                doBackLogic();
//                showBackConfirmDialog();
//                else {
//                    this.finish();
//                }
                break;
            case R.id.call_img:
                if (mConfirmDialog != null) {
                    mConfirmDialog.setDialogContent(patientUserName + "：" + mPhone);
                    mConfirmDialog.show();
                }
                break;
        }
    }


    @Override
    public void onRightBtnClick(View view) {
        super.onRightBtnClick(view);
        if (mConfirmDialog != null) {
            mConfirmDialog.setDialogContent(patientUserName + "：" + mPhone);
            mConfirmDialog.show();
        }
    }

    /**
     * 请求认证状态
     */
    private void requestAuthState() {
        addHttpPostRequest(HttpUrlManager.AUTHENTICATION_STATE_CODE, null, AuthStateResult.class, this);
    }


    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        super.onRequestFinished(taskId, result);
        if (viewpagerMain == null) {
            return;
        }
        switch (taskId) {
            case HttpUrlManager.AUTHENTICATION_STATE_CODE:
                if (result.isRequestSuccessed()) {
                    authStateResult = (AuthStateResult) result.getBodyObject();
                    setCurrentPage(currentPosition);
                } else {
                    if (requestCount < 3) {
                        requestCount++;
                        requestAuthState();
                    } else {
                        RequestErrorToast.showError(this, taskId, result.getCode(), result.getErrorMsg());
                    }
                }
                break;
            case HttpUrlManager.GET_PATIENT_EXPAND_INFO:
                if (result.isRequestSuccessed()) {
                    PatientExpandInfoBean bean = (PatientExpandInfoBean) result.getBodyObject();
                    if (medicationFragment != null) {
                        medicationFragment.showQuickOrderUserTipsLayout(bean);
                    }
                    if (chatFragment != null){
                        chatFragment.showQuickOrderUserTipsLayout(bean);
                    }
                } else {
                    if (medicationFragment != null) {
                        medicationFragment.showQuickOrderUserTipsLayout(null);
                    }
                    if (chatFragment != null){
                        chatFragment.showQuickOrderUserTipsLayout(null);
                    }
                }

                break;
        }
    }

    /**
     * 获取患者附加信息
     */
    private void getPatientExpandInfo() {
        Map map = new HashMap();
        map.put("patientId", patientId);
        getPatientExpandInfoCallback = addHttpPostRequest(HttpUrlManager.GET_PATIENT_EXPAND_INFO, map
                , PatientExpandInfoBean.class, this);
    }


    /**
     * @param activity
     */
//    @Override
//    public void onBack(Activity activity) {
//        cacheTextMsg();//缓存文本消息
//        if (medicationFragment != null && !medicationFragment.isSureExit()) {
//            showBackConfirmDialog();
//        } else {
//            this.finish();
//        }
//        super.onBack(activity);
//    }


    /**
     * 缓存文本消息
     */
    private void cacheTextMsg() {
        if (chatFragment != null) {
            if (dataCacheUtil == null) {
                dataCacheUtil = DataCacheDaoUtil.getInstance();
            }
            dataCacheUtil.upDateData(chatFragment.getTextCacheMsg());
        }
    }

    /**
     * 缓存用药消息
     */
    private void cachePrescriptionMsg() {
        if (medicationFragment != null) {
            if (dataCacheUtil == null) {
                dataCacheUtil = DataCacheDaoUtil.getInstance();
            }
            if (hasShowMedicationFg) {
                dataCacheUtil.upDateData(medicationFragment.setCachePresMsg());
            } else if (medicationParams != null) {
                dataCacheUtil.upDateData(updataCachePresMsg());
            }
        }
    }

    /**
     * 跟新要缓存的药方信息
     *
     * @return
     */
    public DataCache updataCachePresMsg() {
        DataCache data = new DataCache();
        data.setKey(userId + "_" + patientId);
        data.setType(DataCacheType.CACHE_PRESCRIPTION_MSG);
        data.setValue(JSON.toJSONString(medicationParams));
        System.out.println(" 跟新要保存的药方数据：" + patientId + "===============" + JSON.toJSONString(medicationParams));
        return data;
    }

    private void showBackConfirmDialog() {
        if (backConfirmDialog == null) {
            backConfirmDialog = ConfirmDialog.getInstance(this);
        }
        backConfirmDialog.setDialogContent("用药尚未完成，是否退出？")
                .setPositiveText("临时保存")
                .setNavigationText("退出")
                .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                    @Override
                    public void onNavigationBtnClicked(View view) {
                        if (dataCacheUtil == null) {
                            dataCacheUtil = DataCacheDaoUtil.getInstance();
                        }
                        dataCacheUtil.clearCache(medicationFragment.setCachePresMsg());//清除缓存
                        isSaveMedicineMsg = false;
                        backConfirmDialog.dismiss();
                        ChatMainActivity.this.finish();
                    }

                    @Override
                    public void onPositiveBtnClicked(View view) {
                        cachePrescriptionMsg();
                        isSaveMedicineMsg = true;
                        backConfirmDialog.dismiss();
                        ChatMainActivity.this.finish();
                    }
                })
                .show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (dossierFragment != null) {
            dossierFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (chatFragment != null) {
            chatFragment.onActivityResult(requestCode, resultCode, data);
        }
        if (medicationFragment != null) {
            medicationFragment.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventResult(NettyMsgNotify notify) {
        if (notify.getNotifyType() == NotifyType.UPDATA_CHATMAIN_TITILE_MSG) {
            //更新标题信息
            mContacts = mContactsDao.load(patientId + "_" + userId);
            initTitleMsg(mContacts);//初始化标题信息
        }
    }

    @Override
    public void onBackPressed() {
        doBackLogic();
//        if (medicationFragment != null && !medicationFragment.isSureExit()) {
//            showBackConfirmDialog();
//        } else {
//            this.finish();
//        }
//        super.onBackPressed();
    }

    /**
     * 处理返回逻辑
     */
    private void doBackLogic() {
        cacheTextMsg();//缓存文本消息
        //因在onStop（）中缓存了，这里就不需要重复缓存
//        if (medicationFragment != null && !medicationFragment.isSureExit()) {
//            cachePrescriptionMsg();//缓存药方信息
//        }
        this.finish();
    }

    /**
     *
     */
    @Override
    protected void onStop() {
        //当进入用药界面且用药界面的数据有改动后进行药方信息缓存
        try {
            if (medicationFragment != null && !medicationFragment.isSureExit()) {
                cachePrescriptionMsg();
                LogUtils.i(ChatMainActivity.class, "=============medicationFragment != null && !medicationFragment.isSureExit() 缓存药方");
            }
            LogUtils.i(ChatMainActivity.class, "=============ChatMainActivity.class===============onStop()");
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        SharedPreferenceUtils.putString(mContext, PublicParams.PATIENT_USRE_ID,"");
        if (scrollChangeReceiver != null) {
            unregisterReceiver(scrollChangeReceiver);
        }
        if (mConfirmDialog != null) {
            mConfirmDialog = null;
        }
        if (authAlertDialog != null) {
            authAlertDialog = null;
        }
        if (authConfirmDialog != null) {
            authConfirmDialog = null;
        }
        if (backConfirmDialog != null) {
            backConfirmDialog = null;
        }
        cancelRequest(getPatientExpandInfoCallback);
        EventBusUtils.unRegister(this);
        super.onDestroy();
    }


}
