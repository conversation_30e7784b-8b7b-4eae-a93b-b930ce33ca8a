package com.doctor.br.activity.manage

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.View
import org.newapp.ones.base.activity.BaseViewActivity
import com.doctor.br.utils.OrderAndWalletDialog
import org.newapp.ones.base.widgets.ConfirmDialog
import org.newapp.ones.base.network.RequestCallBack
import org.newapp.ones.base.widgets.ActionBarView
import com.doctor.br.httpUrl.HttpUrlManager
import org.newapp.ones.base.network.RequestParams
import org.newapp.ones.base.dataBean.ResponseResult
import org.newapp.ones.base.utils.LogUtils
import com.doctor.br.utils.UIHelper
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.doctor.br.app.AppContext
import com.doctor.br.bean.*
import com.doctor.br.utils.WeChatLoginManager
import com.doctor.br.utils.WeChatLoginManager.LoginCallback
import org.newapp.ones.base.utils.toast.ToastUtils
import org.newapp.ones.base.utils.RequestErrorToast
import com.doctor.br.utils.DialogHelper
import com.doctor.br.view.BottomPopWindow
import com.doctor.yy.R
import com.doctor.yy.databinding.ActivityWxWithDrawBinding
import org.newapp.ones.base.listener.OnButtonClickListener
import org.newapp.ones.base.widgets.ConfirmDialog.OnButtonClickedListener
import org.newapp.ones.base.widgets.InputDialog
import java.lang.Exception
import java.util.HashMap
import java.util.regex.Pattern

/**
 * 提现到微信界面
 */
class WxWithDrawActivity : BaseViewActivity() {
    private var maxMoney = 0.0

    private lateinit var binding: ActivityWxWithDrawBinding

    //验证码
    private var orderAndWalletDialog: OrderAndWalletDialog? = null

    //是否绑定过微信
    private var boundWxOpenId: String? = null
    private var isSelf: Boolean = true

    //最大可提现金额
    private var priceDouble = 0.0
    private var tipsCotnfirmDialog: ConfirmDialog? = null
    private var getMoneyCallBack: RequestCallBack? = null
    private var maxMoneyCallBack: RequestCallBack? = null
    private var doctorInfoCallBack: RequestCallBack? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWxWithDrawBinding.inflate(layoutInflater)
        setActionBarContentView(binding.root)
        setActionBarStyle(ActionBarView.ACTIONBAR_STYLE_WHITE)
        setActionBarTitle("提现到微信零钱")
        init()
        setListener()
        //获取最大提现金额
        maxMoneyCallBack = addHttpPostRequest(HttpUrlManager.CASH_MONEY, null, MaxMoneyBean::class.java, this)
    }


    private fun init() {

        //获取验证码 的dialog
        orderAndWalletDialog = OrderAndWalletDialog(this, false, OrderAndWalletDialog.WhichActivity.WALLET_ACTIVITY) { //验证码校验成功
            //提现
            if (mLoadingDialog != null) {
                mLoadingDialog.setTitle("请求中，请稍候...")
            }
            val params = RequestParams.getGetMoneyByWxParams(priceDouble.toString(), if (isSelf) "1" else "2")
            getMoneyCallBack = addHttpPostRequest(HttpUrlManager.GET_MONEY_BY_WX, params, ResponseResult::class.java, this@WxWithDrawActivity)
            if (orderAndWalletDialog != null) {
                orderAndWalletDialog!!.dismissShowDialog()
            }
        }

        orderAndWalletDialog!!.setTvTitle("短信验证")
        orderAndWalletDialog!!.setBtnCancel("取消")
        orderAndWalletDialog!!.setBtnConfirm("确认")
        orderAndWalletDialog!!.isJustCheckCode(true)
        boundWxOpenId = AppContext.getInstances().loginInfo.wxOpenId
        isSelf = true
        updateWxBind()
        if (TextUtils.isEmpty(boundWxOpenId) || TextUtils.isEmpty(AppContext.getInstances().loginInfo.supportWxHome)) {
            doctorInfoCallBack = addHttpPostRequest(HttpUrlManager.DOCTOR_INFO, null, DoctorInfoBean::class.java, this)
        }
        regToWx()
    }

    private fun updateWxBind() {
        if (AppContext.getInstances().loginInfo.supportWxHome == "1") {
            if (isSelf) {
                binding.tvWx.text = "提现至微信零钱(本人)"
                binding.tvWxBind.text = if (TextUtils.isEmpty(boundWxOpenId)) "请绑定本人微信" else "已绑定本人微信"
                binding.tvWxBind.tag = if (TextUtils.isEmpty(boundWxOpenId)) "1" else "2"
            } else {
                binding.tvWx.text = "提现至微信零钱(亲属)"
                binding.tvWxBind.text = if (TextUtils.isEmpty(boundWxOpenId)) "请绑定亲属微信" else "已绑定亲属微信"
                binding.tvWxBind.tag = if (TextUtils.isEmpty(boundWxOpenId)) "3" else "4"
            }
            binding.ivWxArrow.visibility = View.VISIBLE
        } else {
            binding.tvWx.text = "提现至微信零钱"
            binding.tvWxBind.text = if (TextUtils.isEmpty(boundWxOpenId)) "请绑定微信" else "已绑定微信"
            binding.tvWxBind.tag = if (TextUtils.isEmpty(boundWxOpenId)) "1" else "2"
            binding.ivWxArrow.visibility = View.GONE
        }
    }

    private fun setListener() {
        binding.rlRule.setOnClickListener(this)
        binding.btnGetMoney.setOnClickListener(this)
        binding.tvWxBind.setOnClickListener(this)
        binding.rlBindWx.setOnClickListener(this)

        binding.etMoney.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence, start: Int, before: Int, count: Int) {
                // 如果是空字符串，直接返回
                if (s.toString().isNullOrEmpty()) return

                var currentString = s.toString()
                var futureString = currentString

                // 替换中文句号为英文小数点
                if (currentString.contains("。")) {
                    futureString = currentString.replace("。", ".")
                    binding.etMoney.setText(futureString)
                    binding.etMoney.setSelection(futureString.length)
                    return
                }

                // 处理首位输入小数点的情况
                if (futureString.startsWith(".")) {
                    futureString = "0$futureString"
                    binding.etMoney.setText(futureString)
                    binding.etMoney.setSelection(futureString.length)
                    return
                }

                // 处理以0开头的情况
                if (futureString.length > 1 && futureString.startsWith("0") && !futureString.startsWith("0.")) {
                    binding.etMoney.setText(futureString.substring(0, 1))
                    binding.etMoney.setSelection(1)
                    return
                }

                // 限制小数点后最多两位
                if (futureString.contains(".")) {
                    val dotIndex = futureString.indexOf(".")
                    if (futureString.length - dotIndex > 3) {
                        futureString = futureString.substring(0, dotIndex + 3)
                        binding.etMoney.setText(futureString)
                        binding.etMoney.setSelection(futureString.length)
                        return
                    }
                }

                // 修改: 限制整数部分长度不超过4位,并添加Toast提示
                val integerPart = if (futureString.contains("."))
                    futureString.split(".")[0] else futureString
                if (integerPart.length > 4) {
                    // 添加: 当尝试输入超过4位数时显示Toast提示
                    ToastUtils.showShortMsg(this@WxWithDrawActivity, "本次最大提现金额不能超过9999")

                    futureString = if (futureString.contains(".")) {
                        val parts = futureString.split(".")
                        "${parts[0].substring(0, 4)}.${parts[1]}"
                    } else {
                        futureString.substring(0, 4)
                    }
                    binding.etMoney.setText(futureString)
                    binding.etMoney.setSelection(futureString.length)
                    return
                }

                // 验证金额上限
                try {
                    val inputAmount = futureString.toDouble()
                    // 修改: 使用 Math.min 确保实际最大可提现金额不超过9999
                    val actualMaxAmount = Math.min(maxMoney.toDouble(), 9999.0)

                    // 修改: 如果输入金额超过实际最大可提现金额，给出提示并修正输入值
                    if (inputAmount > actualMaxAmount) {
                        ToastUtils.showShortMsg(
                            this@WxWithDrawActivity,
                            String.format("本次最大提现金额为%.2f", actualMaxAmount)
                        )
                        val maxString = String.format("%.2f", actualMaxAmount)
                        binding.etMoney.setText(maxString)
                        binding.etMoney.setSelection(maxString.length)
                        return
                    }
                } catch (e: NumberFormatException) {
                    // 处理数字格式异常
                    return
                }
            }

            override fun afterTextChanged(s: Editable) {}
        })
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.rl_rule -> UIHelper.openGetMoneyRuleActivity(this, true)
            R.id.btn_get_money -> if (checkGetMoneyParam()) { //校验
                if (priceDouble < 999) { //提现金额<999
                    tipsCotnfirmDialog = DialogHelper.openConfirmDialog(this, "您的提现金额未满999，需要您支付1元手续费，是否提现？", "提现", "取消") {
                        tipsCotnfirmDialog!!.dismiss()
                        orderAndWalletDialog!!.show()
                    }
                    tipsCotnfirmDialog?.show()
                } else { //提现金额>=999
                    orderAndWalletDialog!!.show()
                }
            }
            R.id.tv_wx_bind -> {
                if (isSelf) {
                    bindWx("")
                } else {
                    InputDialog.getInstance(this).apply {
                        setInputMaxLength(20)
                        setInputHint("请输入亲属微信认证姓名")
                        setInputTitle("绑定微信")
                        setOnButtonClickListener(object : OnButtonClickListener {
                            override fun onPositiveClick(view: View?, editText: CharSequence?) {
                                bindWx(editText?.toString() ?: "")
                                dismiss()
                            }

                            override fun onNegativeClick(view: View?, editText: CharSequence?) {
                                dismiss()
                            }

                        })
                        show()
                    }.show()
                }
            }
            R.id.rl_bind_wx -> {
                if (AppContext.getInstances().loginInfo.supportWxHome != "1") {
                    return
                }
                BottomPopWindow(this).apply {
                    setPopContentData(listOf(PopItem().apply {
                        this.id = "1"
                        this.value = AppContext.getInstances().loginInfo.wxOpenId
                        this.name = "提现至微信零钱(本人)"
                    }, PopItem().apply {
                        this.id = "2"
                        this.value = AppContext.getInstances().loginInfo.wxOpenIdHome
                        this.name = "提现至微信零钱(亲属)"
                    }))
                    setOnItemClickListener { position, popItem ->
                        isSelf = "1" == popItem.id
                        boundWxOpenId = popItem.value
                        updateWxBind()
                        dimissPop()
                    }
                }.showPop()
            }
        }
    }

    // IWXAPI 是第三方app和微信通信的openApi接口
    private val api: IWXAPI? = null

    private fun regToWx() {
        WeChatLoginManager.registerToWxApp(this)
    }

    private fun bindWx(accountName: String) {
        if (mLoadingDialog != null) {
            mLoadingDialog.setTitle("绑定中，请稍候...")
            mLoadingDialog.show()
        }
        val success = WeChatLoginManager.login(this, "aaa", object : LoginCallback {
            override fun onLoginSuccessful(authCode: String?) {
                val param: MutableMap<String, String> = HashMap()
                param["code"] = authCode ?: ""
                param["type"] = if (isSelf) "1" else "2"
                if (!isSelf) {
                    param["accountName"] = accountName
                }
                addHttpPostRequest(HttpUrlManager.BINd_WX, param, BindWxBean::class.java, this@WxWithDrawActivity)
            }

            override fun onLoginFail(code: Int, message: String?) {
                if (mLoadingDialog != null) {
                    mLoadingDialog.dismiss()
                }
                ToastUtils.showShortMsg(this@WxWithDrawActivity, message ?: "绑定微信失败")
            }
        })
        if (!success) {
            ToastUtils.showShortMsg(this, "打开微信失败，无法绑定！")
        }
    }

    /**
     * 校验提现前的参数
     *
     * @return
     */
    private fun checkGetMoneyParam(): Boolean {
        val price = binding.etMoney.text.toString().trim { it <= ' ' }
        if (TextUtils.isEmpty(boundWxOpenId)) {
            ToastUtils.showShortMsg(this, "请先绑定微信！")
            return false
        }
        if (TextUtils.isEmpty(price)) {
            ToastUtils.showShortMsg(this, "请输入提现金额")
            return false
        } else {
            val regEx = "^(([0-9]+\\.[0-9]*[0-9][0-9]*)|([0-9]*[0-9][0-9]*\\.[0-9]+)|([0-9]*[0-9][0-9]*))$"
            val pattern = Pattern.compile(regEx)
            if (pattern != null) {
                val matcher = pattern.matcher(price)
                if (matcher != null) {
                    val b = matcher.matches()
                    if (!b) {
                        ToastUtils.showShortMsg(this, "提现金额只能是数字！")
                        return false
                    }
                }
            }
        }
        priceDouble = try {
            java.lang.Double.valueOf(price) //转换为double类型
        } catch (e: Exception) {
            0.0
        }
        if (maxMoney < priceDouble) {
            ToastUtils.showShortMsg(this, "您今日可提现的金额不得大于" + maxMoney + "元")
            return false
        }
        if (0.0 == priceDouble) {
            ToastUtils.showShortMsg(this, "请输入正确提现金额！")
            return false
        }
        return true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        super.onActivityResult(requestCode, resultCode, intent)
        //        if (requestCode == REQUEST_BIND_CARD && resultCode == RESULT_OK) {//获取绑定银行卡界面返回的数据
//            String cardUserName = intent.getStringExtra(BindCardActivity.RESULT_EXTRA_CARD_USERNAME);
//            String cardnumber = intent.getStringExtra(BindCardActivity.RESULT_EXTRA_CARD_NUMBER);
//            String cardType = intent.getStringExtra(BindCardActivity.RESULT_EXTRA_CARD_TYPE);
//
//            if (!TextUtils.isEmpty(cardnumber)) { //设置卡号
//                this.cardNumber = cardnumber;
//                String payContent = "";
//                if (cardNumber.length() >= 4) {
//                    payContent = "(" + cardNumber.substring(cardNumber.length() - 4, cardNumber.length()) + ")";
//                } else {
//                    payContent = "(" + cardNumber + ")";
//                }
//
//                tvNumber.setText(payContent);
//            }
//            if (!TextUtils.isEmpty(cardType)) {//设置卡类型
//                this.cardType = cardType;
//                tvCardType.setHint("");
//                tvCardType.setText(cardType);
//            }
//            if (!TextUtils.isEmpty(cardUserName)) {
//                this.cardName = cardUserName;
//            }
//
//
//        }
    }

    override fun onRequestFinished(taskId: String, result: ResponseResult) {
        super.onRequestFinished(taskId, result)
        when (taskId) {
            HttpUrlManager.GET_MONEY_BY_WX -> if (result.isRequestSuccessed) {
                ToastUtils.showShortMsg(this, "提交成功，请等候")
                setResult(Activity.RESULT_OK)
                finish()
            } else { //网络请求失败
                if (TextUtils.equals(result.code, "0016")) {
                    ToastUtils.showShortMsg(this, "绑定的微信和认证姓名不一致，请联系客服处理")
                } else if (TextUtils.equals(result.code, "0017")) {
                    ToastUtils.showShortMsg(this, "微信账户余额不足，请减少提现金额后重试")
                } else {
                    RequestErrorToast.showError(this, taskId, result.code, result.errorMsg)
                }
            }
            HttpUrlManager.CASH_MONEY -> if (result.isRequestSuccessed) {
                val maxMoneyBean = result.bodyObject as MaxMoneyBean?
                if (maxMoneyBean != null) {
                    maxMoney = maxMoneyBean.canCashPrice.toDouble()
                    if (maxMoney == 0.0) { //测试让显示0.00元
                        binding.etMoney.hint = "本次可提现0.00元"
                    } else {
                        binding.etMoney.hint = "本次可提现" + maxMoney + "元"
                    }
                    binding.tvWithDrawMax.text = "可提现金额:" + maxMoney + "元"
                }
            } else { //网络请求失败
                RequestErrorToast.showError(this, taskId, result.code, result.errorMsg)
                finish()
            }
            HttpUrlManager.BINd_WX -> if (result.isRequestSuccessed) {
                if (result.bodyObject is BindWxBean) {
                    if (AppContext.getInstances().loginInfo != null) {
                        LogUtils.i(WxWithDrawActivity::class.java, "bindWx success")
                        val userInfo = AppContext.getInstances().loginInfo
                        if (isSelf) {
                            userInfo.wxOpenId = (result.bodyObject as BindWxBean).wx_openid
                        } else {
                            userInfo.wxOpenIdHome = (result.bodyObject as BindWxBean).wx_openid
                        }
                        boundWxOpenId = (result.bodyObject as BindWxBean).wx_openid
                        AppContext.getInstances().saveLoginInfo(userInfo)
                        updateWxBind()
                        ToastUtils.showShortMsg(this@WxWithDrawActivity, "绑定成功")
                    } else {
                        ToastUtils.showShortMsg(this@WxWithDrawActivity, "绑定微信失败[-2]")
                    }
                } else {
                    ToastUtils.showShortMsg(this@WxWithDrawActivity, "绑定微信失败[-1]")
                }
            } else {
                ToastUtils.showShortMsg(this@WxWithDrawActivity, "绑定微信失败")
            }
            HttpUrlManager.DOCTOR_INFO -> if (result.isRequestSuccessed) {
                val bean = result.bodyObject as DoctorInfoBean
                val userInfo = AppContext.getInstances().loginInfo
                if (userInfo != null) {
                    userInfo.wxOpenId = bean.wxOpenId
                    userInfo.supportWxHome = bean.supportWxHome
                    userInfo.wxOpenIdHome = bean.wxOpenIdHome
                    AppContext.getInstances().saveLoginInfo(userInfo)
                    boundWxOpenId = bean.wxOpenId
                    isSelf = true
                    updateWxBind()
                }
            } else {
                ToastUtils.showShortMsg(this@WxWithDrawActivity, "请求失败，请稍候重试")
            }
        }
    }

    override fun onDestroy() {
        cancelRequest(getMoneyCallBack)
        cancelRequest(maxMoneyCallBack)
        if (orderAndWalletDialog != null) {
            orderAndWalletDialog!!.cancel()
            orderAndWalletDialog = null
        }
        if (tipsCotnfirmDialog != null) {
            tipsCotnfirmDialog!!.dismiss()
            tipsCotnfirmDialog = null
        }
        super.onDestroy()
    }


}