package com.doctor.br.activity.mine;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;

import com.doctor.br.activity.ForgetPasswordActivity;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;

import butterknife.BindView;

/**
 * 类描述：安全设置界面
 * 创建人：ShiShaoPo
 * 创建时间：2017/12/4
 */

public class SafeSettingActivity extends ActionBarActivity {
    //界面下的控件
    @BindView(R.id.modify_phone_rl)
    RelativeLayout modifyPhoneRl;//更换手机号
    @BindView(R.id.modify_password_rl)
    RelativeLayout modifyPasswordRl;//更改登录密码

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_safe_setting);
        getBaseActionBar().setActionBarTitle("安全设置");
        setListener();
    }

    private void setListener() {
        modifyPhoneRl.setOnClickListener(this);
        modifyPasswordRl.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.modify_phone_rl://更换手机号
                startActivity(new Intent(this, ModifyPhoneActivity.class));
                break;
            case R.id.modify_password_rl://修改密码
                Intent intent = new Intent(this, ForgetPasswordActivity.class);
                intent.putExtra(ForgetPasswordActivity.WITCH_ACTIVITY, SafeSettingActivity.class.getSimpleName());
                startActivity(intent);
                break;
            default:
                break;
        }
    }
}
