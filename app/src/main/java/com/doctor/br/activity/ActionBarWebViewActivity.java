package com.doctor.br.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.alibaba.fastjson.JSON;
import com.doctor.br.activity.chatmain.ChatHistoryRecordsActivity;
import com.doctor.br.activity.chatmain.ChatMainActivity;
import com.doctor.br.activity.mine.PrivacyPolicyActivity;
import com.doctor.br.adapter.chatmain.ChatAdapter;
import com.doctor.br.bean.PopItem;
import com.doctor.br.utils.BroadcastAction;
import com.doctor.yy.R;

import org.newapp.ones.base.activity.ActionBarActivity;
import org.newapp.ones.base.base.BaseConfig;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.utils.ActivityManager;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.widgets.EmptyView;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @project BrZhongYiAndroid
 * @description 有ActionBar的webView页面
 * @createTime 2017/10/26  1
 */

public class ActionBarWebViewActivity extends ActionBarActivity {
    private WebView mWebView;
    private EmptyView mEmptyView;
    private String mLoadUrl;
    private String mTitle;
    public boolean isLoadError;
    private String mFromPage;

    public static void start(Context context,String title,String url){
        Intent intent = new Intent(context, ActionBarWebViewActivity.class);
        intent.putExtra(PublicParams.WEBVIEW_LOAD_URL, url);
        intent.putExtra(PublicParams.WEBVIEW_TITLE,title);
        intent.putExtra(PublicParams.WEBVIEW_FROM_PAGE,"");
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setActionBarContentView(R.layout.activity_actionbar_webview);
        mLoadUrl = getIntent().getStringExtra(PublicParams.WEBVIEW_LOAD_URL);
        mTitle = getIntent().getStringExtra(PublicParams.WEBVIEW_TITLE);
        mFromPage = getIntent().getStringExtra(PublicParams.WEBVIEW_FROM_PAGE);
        if (!TextUtils.isEmpty(mTitle)) {
            setActionBarTitle(mTitle);
        }
        initViews();
        mWebView.loadUrl(mLoadUrl);
    }

    /**
     * 初始化view
     */
    @SuppressLint("JavascriptInterface")
    private void initViews() {
        mEmptyView = (EmptyView) findViewById(R.id.empty_view);
        mEmptyView.setOnReloadListener(new EmptyView.OnReloadListener() {
            @Override
            public void onReload() {
                mEmptyView.setEmptyType(EmptyView.TYPE_LOADING);
                mWebView.reload();
            }
        });
        mWebView = (WebView) findViewById(R.id.web_view);
        WebSettings settings = mWebView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setAppCacheEnabled(true);
        settings.setBuiltInZoomControls(true);
        settings.setLayoutAlgorithm(WebSettings.LayoutAlgorithm.NARROW_COLUMNS);
        settings.setUseWideViewPort(true);
        settings.setLoadWithOverviewMode(true);
        settings.setSaveFormData(true);
        settings.setGeolocationEnabled(true);
        if (Build.VERSION.SDK_INT >= 21) {//Android 从Lollipop(5.0)开始 webview默认不允许混合模式，https当中不能加载http资源，如果要加载，需单独设置开启。
            settings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }
        settings.setDomStorageEnabled(true);
        //不显示webview缩放按钮
        settings.setDisplayZoomControls(false);
        //不支持屏幕缩放
        settings.setSupportZoom(false);
        settings.setBuiltInZoomControls(false);
        mWebView.requestFocus();
        mWebView.addJavascriptInterface(new JsInterface(), "control");
        mWebView.setWebViewClient(new WebViewClient() {

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    view.loadUrl(request.getUrl().toString());
                }
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return true;
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                super.onReceivedError(view, request, error);
//                isLoadError = true;
//                if (isLoadError) {
//                    mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
//                } else {
//                    mEmptyView.setEmptyType(EmptyView.TYPE_LOADING);
//                    mEmptyView.hide();
//                }
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                isLoadError = true;
                if (isLoadError) {
                    mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                } else {
                    mEmptyView.setEmptyType(EmptyView.TYPE_LOADING);
                    mEmptyView.hide();
                }
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.proceed();
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                if (isLoadError) {
                    mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                } else {
                    mEmptyView.setEmptyType(EmptyView.TYPE_LOADING);
                    mEmptyView.hide();
                }
            }
        });

        mWebView.setWebChromeClient(new WebChromeClient() {


            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                if (!TextUtils.isEmpty(title) && title.toLowerCase().contains("error")) {
                    isLoadError = true;
                    if (isLoadError) {
                        mEmptyView.setEmptyType(EmptyView.TYPE_RELOAD);
                    } else {
                        mEmptyView.hide();
                    }
                } else {
                    isLoadError = false;
                }
            }
        });

        mWebView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                return true;
            }
        });
    }


    /**
     * js交互的类和方法
     */
    class JsInterface {
        @JavascriptInterface
        public void closeWindow(String json) {
            ActionBarWebViewActivity.this.finish();
        }

        @JavascriptInterface
        public void toUseDrug(String json) {
            Intent intent = new Intent(BroadcastAction.SET_CHAT_MAIN_POSITION);
            intent.putExtra(ChatMainActivity.POSITION, 2);
            intent.putExtra(PublicParams.TO_MEDICATION_FROM_PAGE,PublicParams.FROM_WZD_OR_FZD);
            try {
                PopItem popItem = null;
                if (!TextUtils.isEmpty(json) && !"undefined".equalsIgnoreCase(json)) {
                    popItem = JSON.parseObject(json, PopItem.class);
                }
                if (popItem != null) {
                    intent.putExtra(ChatMainActivity.TAKER_INFO, (Serializable) popItem);
                }
            } catch (Exception e) {
                LogUtils.e(ActionBarWebViewActivity.class, e.getMessage());
//                e.printStackTrace();
            }
            sendBroadcast(intent);
            setResult(RESULT_OK);
            if (ChatAdapter.FROM_CHAT_HISTORY_RECORDS.equalsIgnoreCase(mFromPage)) {
                ActivityManager.getInstance().removeActivity(ChatHistoryRecordsActivity.class);
            }
            ActionBarWebViewActivity.this.finish();

        }
    }
}
