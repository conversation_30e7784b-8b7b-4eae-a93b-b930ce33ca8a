package com.doctor.br.fragment.chatmain;


import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;

import androidx.appcompat.widget.SwitchCompat;

import android.text.Editable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.Pair;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.TextView;

import com.alibaba.fastjson.JSON;
import com.doctor.br.activity.BaiTaiActivity;
import com.doctor.br.activity.medical.AddMedicineActivity;
import com.doctor.br.activity.medical.AddPatientActivity;
import com.doctor.br.activity.medical.PreviewMedicationActivity;
import com.doctor.br.activity.medical.QuicklyPrescriptionActivity;
import com.doctor.br.app.AppContext;
import com.doctor.br.bean.DataCacheType;
import com.doctor.br.bean.PatientExpandInfoBean;
import com.doctor.br.bean.PatientMsgBean;
import com.doctor.br.bean.PopItem;
import com.doctor.br.bean.ServiceSettingBean;
import com.doctor.br.bean.event.FormAndOrderMsgBean;
import com.doctor.br.bean.event.PatientChooseChangeEvent;
import com.doctor.br.bean.event.PatientInfoChangeEvent;
import com.doctor.br.bean.medical.DoctorServiceFeeRadioBean;
import com.doctor.br.bean.medical.DosageFormBean;
import com.doctor.br.bean.medical.MedicineDetailMsgBean;
import com.doctor.br.bean.medical.OrderMsgBean;
import com.doctor.br.bean.medical.TabooTipsBean;
import com.doctor.br.bean.medical.TakingTimeBean;
import com.doctor.br.db.entity.Contacts;
import com.doctor.br.db.entity.DataCache;
import com.doctor.br.db.entity.Session;
import com.doctor.br.db.utils.DataCacheDaoUtil;
import com.doctor.br.httpUrl.HttpUrlManager;
import com.doctor.br.netty.NettyResult;
import com.doctor.br.netty.model.Message;
import com.doctor.br.netty.model.PatientMsg;
import com.doctor.br.utils.DecimalUtils;
import com.doctor.br.utils.EventBusUtils;
import com.doctor.br.view.AuxiliaryMaterialPopWindow;
import com.doctor.br.view.BottomPopWindow;
import com.doctor.br.view.DosageFormsPop;
import com.doctor.br.view.FlowLayout;
import com.doctor.br.view.TabooTipsPopWindow;
import com.doctor.greendao.gen.ContactsDao;
import com.doctor.greendao.gen.SessionDao;
import com.doctor.yy.R;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.newapp.ones.base.base.PublicParams;
import org.newapp.ones.base.dataBean.ResponseResult;
import org.newapp.ones.base.fragment.BaseFragment;
import org.newapp.ones.base.listener.OnButtonClickListener;
import org.newapp.ones.base.network.RequestCallBack;
import org.newapp.ones.base.utils.DensityUtils;
import org.newapp.ones.base.utils.LogUtils;
import org.newapp.ones.base.utils.RequestErrorToast;
import org.newapp.ones.base.utils.SharedPreferenceForeverUtils;
import org.newapp.ones.base.utils.SharedPreferenceUtils;
import org.newapp.ones.base.utils.toast.ToastUtils;
import org.newapp.ones.base.widgets.AlertDialog;
import org.newapp.ones.base.widgets.ConfirmDialog;
import org.newapp.ones.base.widgets.InputDialog;
import org.newapp.ones.base.widgets.NoEmojiEditText;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;

import static android.app.Activity.RESULT_OK;

/**
 * Author:sunxiaxia
 * createdDate:2017/9/27
 * description:用药fragment
 */

public class MedicationFragment extends BaseFragment {


    @BindView(R.id.patient_name_tv)
    TextView patientNameTv;
    @BindView(R.id.is_patient_self)
    TextView isPatientSelf;
    @BindView(R.id.sex_img)
    ImageView sexImg;
    @BindView(R.id.age_tv)
    TextView ageTv;
    @BindView(R.id.is_pregnant_tv)
    TextView isPregnantTv;
    @BindView(R.id.select_patient_btn)
    Button mSelectPatientBtn;
    @BindView(R.id.diagnose_et)
    EditText diagnoseEt;
    @BindView(R.id.clear_tv)
    TextView mClearTv;
    @BindView(R.id.add_medicines_ll)
    LinearLayout addMedicinesLl;
    @BindView(R.id.total_dose_et)
    EditText totalDoseEt;
    @BindView(R.id.day_dose_et)
    EditText dayDoseEt;
    @BindView(R.id.pre_dose_time_et)
    EditText preDoseTimeEt;
    @BindView(R.id.select_taking_time_img)
    ImageView mSelectTakingTimeImg;
    @BindView(R.id.taking_time_tv)
    TextView takingTimeTv;
    @BindView(R.id.day_after)
    TextView dayAfter;
    @BindView(R.id.after_day_et)
    EditText afterDayEt;
    @BindView(R.id.select_taboo)
    TextView selectTaboo;
    @BindView(R.id.direction_et)
    NoEmojiEditText directionEt;
    @BindView(R.id.template_cb)
    CheckBox templateCb;
    @BindView(R.id.privary_cb)
    CheckBox privaryCb;

    @BindView(R.id.privary_dose_cb)
    CheckBox privaryDoseCb;

    @BindView(R.id.template_name_et)
    NoEmojiEditText templateNameEt;
    @BindView(R.id.note_et)
    EditText noteEt;
    @BindView(R.id.medicine_totalprice_tv)
    TextView medicineTotalPriceTv;
    @BindView(R.id.additional_charge_tv)
    TextView additionalChargeTv;
    @BindView(R.id.additional_charge_et)
    EditText additionalChargeEt;
    @BindView(R.id.older_totalprice_tv)
    TextView olderTotalpriceTv;
    @BindView(R.id.generate_basis_btn)
    Button generateBasisBtn;
    @BindView(R.id.dotted_line)
    ImageView mDottedLine;
    @BindView(R.id.total_price_tv)
    TextView mTotalPriceTv;
    @BindView(R.id.layout_root)
    RelativeLayout mLayoutRoot;
    @BindView(R.id.medicine_container)
    FlowLayout medicineContainer;
    @BindView(R.id.taboo_tv)
    TextView tabooTv;
    @BindView(R.id.direction_tv)
    TextView mDirectionTv;
    @BindView(R.id.ll_edit)
    LinearLayout mEditTv;
    @BindView(R.id.normal_usage_and_dosage_ll)
    LinearLayout normalUsageAndDosageLl;
    @BindView(R.id.special_total_dose_tv)
    TextView specialTotalDoseTv;
    @BindView(R.id.special_make_description_tv)
    TextView specialMakeDescriptionTv;
    @BindView(R.id.preday_dose_et)
    EditText predayDoseEt;
    @BindView(R.id.pretime_dose_et)
    EditText pretimeDoseEt;
    @BindView(R.id.pretime_dose_label_tv)
    TextView pretimeDoseLabelTv;
    @BindView(R.id.pretime_dose_unit_tv)
    TextView pretimeDoseUnitTv;
    @BindView(R.id.pretime_dose_gram_tv)
    TextView pretimeDoseGramTv;
    @BindView(R.id.capsule_num_tv)
    TextView capsuleNumTv;
    @BindView(R.id.take_day_tv)
    TextView takeDayTv;
    @BindView(R.id.special_usage_and_dosage_ll)
    LinearLayout specialUsageAndDosageLl;
    @BindView(R.id.specification_ll)
    LinearLayout specificationLl;
    @BindView(R.id.specification_tv)
    TextView specificationTv;
    @BindView(R.id.specification_arrow_img)
    ImageView specificationArrowImg;
    @BindView(R.id.specification_line)
    ImageView specificationLine;
    @BindView(R.id.total_dose_line)
    ImageView totalDoseLine;
    @BindView(R.id.make_medicine_price_ll)
    LinearLayout makeMedicinePriceLl;
    @BindView(R.id.make_medicine_price_tv)
    TextView makeMedicinePriceTv;
    @BindView(R.id.medicine_form_supplier)
    TextView medicineFormSupplier;
    @BindView(R.id.medicine_mess_ll)
    LinearLayout medicineMessLl;
    @BindView(R.id.sure_diavisiblesed_cb)
    CheckBox sureDiavisiblesedCb;
    @BindView(R.id.sex_age_ll)
    LinearLayout sexAgeLl;
    Unbinder unbinder;
    @BindView(R.id.switch_btn)
    SwitchCompat switchBtn;
    @BindView(R.id.price_detail_tv)
    TextView priceDetailTv;
    @BindView(R.id.show_price_detail_iv)
    ImageView showPriceDetailIv;
    @BindView(R.id.base_medicine_price_tv)
    TextView baseMedicinePriceTv;
    @BindView(R.id.doctor_service_price_et)
    EditText doctorServiceFeeEt;
    @BindView(R.id.price_layout)
    LinearLayout medicinePriceLayout;
    @BindView(R.id.hide_price_detail_iv)
    ImageView hidePriceDetailIv;
    @BindView(R.id.scrollView)
    ScrollView scrollView;
    @BindView(R.id.bottom_view)
    LinearLayout bottomView;//显示医技服务费提醒
    @BindView(R.id.yiji_relative)
    RelativeLayout medicalServiceRl;
    @BindView(R.id.daijian_layout)
    RelativeLayout daiJianRl;
    @BindView(R.id.daijian_preference_layout)
    RelativeLayout daiJianPianhaoRl;
    @BindView(R.id.daijian_line)
    View daiJianLine;
    @BindView(R.id.daijian_preference_line)
    View daijianPianhaoLine;
    @BindView(R.id.daijian_select_tv)
    TextView daiJianPianhaoSelectTv;
    @BindView(R.id.daijian_switch_btn)
    SwitchCompat daiJianSwitch;
    @BindView(R.id.quick_order_user_tips_layout)
    View quickOrderUserTipsLayout;
    @BindView(R.id.go_to_quick_order)
    View goToQuickOrder;
    @BindView(R.id.iv_mark)
    ImageView ivMark;
    @BindView(R.id.base_medicine_info_icon)
    ImageView baseMedicineInfoIcon;
    @BindView(R.id.additional_charge_info_icon)
    ImageView additionalChargeInfoIcon;
    @BindView(R.id.iv_sum_mark)
    ImageView ivSumMark;
    @BindView(R.id.btn_internal_use)
    Button btnInternalUse;
    @BindView(R.id.btn_external_use)
    Button btnExternalUse;
    @BindView(R.id.usage_method_layout)
    RelativeLayout usageMethodLayout;
    @BindView(R.id.auxiliary_material_ll)
    LinearLayout auxiliaryMaterialLl;
    @BindView(R.id.auxiliary_material_tv)
    TextView auxiliaryMaterialTv;
    @BindView(R.id.auxiliary_material_arrow_img)
    ImageView auxiliaryMaterialArrowImg;
    @BindView(R.id.auxiliary_material_line)
    ImageView auxiliaryMaterialLine;


    private static final String PRICE_CONVERSION_RATIO = "0.7";//药价换算比例
    private Context mContext;
    private BottomPopWindow mBottomPopWindow;
    private TabooTipsPopWindow mTabooTipsPopWindow;
    private AuxiliaryMaterialPopWindow mAuxiliaryMaterialPopWindow;
    private List<PopItem> patientPopItems;
    private List<PopItem> timePopItems;
    private List<PopItem> daijianPianhaoPopItems;
    //当前代煎偏好选择索引
    private int daiJianPianhaoIndex = 0;
    //胶囊选项弹窗
    private List<PopItem> typePopItems;

    private List<PopItem> takingWayPopItems;
    private List<TabooTipsBean.TipBean> tipsItems;
    private String mMedicineType;
    private String mTakingWay;
    private static final int REQUESTCODE = 111;
    private static final int REQUESTCODE_EDIT = 112;
    private RequestCallBack mRequestCallBack;
    private List<PatientMsgBean.PatientsBean> mPatients;
    private InputDialog mInputDialog;
    //标识是否设置当前患者信息，首次进入，该值为false，表示未设置，则需要设置，为true时，表示已经设置过（不是第一次请求结果），不设置
    private boolean isSetPatient;
    //当刚进入界面时用药时间列表数据获取失败，点击"选择用药时间"时就先请求数据，数据请求成功后显示弹框，默认请求成功不显示弹框
    private boolean showTimePop;
    //当刚进入界面时服药禁忌列表数据获取失败，点击"选择服药禁忌"时就先请求数据，数据请求成功后显示弹框，默认请求成功不显示弹框
    private boolean showTipsPop;
    //当刚进入界面时剂型列表数据获取失败，点击"添加药材"时就先请求数据，数据请求成功后显示弹框，默认请求成功不显示弹框
    private boolean showDosageFormPop;
    private ConfirmDialog mConfirmDialog;
    private DosageFormsPop mDosageFormsPop;
    private List<DosageFormBean.FormList> mDosageFormList;
    private List<MedicineDetailMsgBean> mMedicineList;
    private String isPregnant;
    private String mDrugForm;
    private OrderMsgBean medicationParams;
    private String takerSex;
    private String takerAge;
    private String takerName;
    //服药者id
    private String takerId;
    //患者本人id
    private String patientId;
    private DosageFormBean.FormList.FormDetailMsg mFormDetailMsg;
    private DosageFormBean.FormList mFormListBean;
    private String medicineFee;
    //医技服务费比例
    private String newDoctorServiceRadio;
    private int leftP;
    private String rightP;
    private RequestCallBack getMedicalFeeCallBack;
    private RequestCallBack getDosageFormListCallBack;
    private RequestCallBack getTabooTipsCallBack;
    private RequestCallBack addCustomTakingTimeCallBack;
    private RequestCallBack getPatientsCallBack;
    private RequestCallBack getTakingTimeCallBack;
    private RequestCallBack getMedicalServiceFeeRadioCallBack;
    private String userId;
    public boolean isGetCache = true;//是否获取缓存信息；初次显示此界面获取，
    private int clickCount = 0;
    private boolean isShowSelectPatientPop = true;//是否显示选择开方患者的弹框，没有缓存时首次请求完数据不弹框，默认显示患者本人
    private boolean isEditing;
    private String medicalFeeRemark = "";
    private String[] cacheTaboos;

    private boolean mIsAutoSend;//记录是否自动发送复诊单，用于退出时判断是否进行用药信息缓存

    private int showTipTimes;//已经显示医技服务费提示语的次数
    private boolean isShowPoped;//是否已经显示医技服务费为0元时的提示框
    private boolean isShowMedicalServiceFee;//是否显示医技服务费
    private AlertDialog tipsPop;//医技服务费为0时的提示
    private RequestCallBack saveCommonTemplateCallBack;//新增常用方
    private ContactsDao contactsDao;
    private SessionDao sessionDao;

    
    //自定义蜜丸用量的弹窗
    private com.doctor.br.view.InputDialog customPillDosageDialog;
    
    //自定义胶囊用量的弹窗
    private com.doctor.br.view.InputDialog customCapsuleDosageDialog;
    
    //自定义水丸用量的弹窗
    private com.doctor.br.view.InputDialog customWaterPillDosageDialog;
    
    //自定义膏方用量的弹窗
    private com.doctor.br.view.InputDialog customCreamFormulaDosageDialog;

    //创建map用来保存对应的剂型的predayDoseEt输入的内容，下次切换的时候用来显示之前输入的内容
    private Map<String, String> predayDoseEtMap = new HashMap<String, String>();

    //创建map用来保存对应的剂型的pretimeDoseEt输入的内容，下次切换的时候用来显示之前输入的内容
    private Map<String, String> pretimeDoseEtMap = new HashMap<>();

    final String preDayDoseKey = "preDayDoseEt_";
    final String preTimeDoseKey = "preTimeDoseEt_";

    // 规格选择相关变量
    private List<PopItem> specificationItems = new ArrayList<>(); // 规格选择数据
    private String selectedSpecification = ""; // 当前选中的规格
    
    // 蜜丸剂型规格解析相关变量
    private String currentSpecUnit = ""; // 当前规格的单位（如"丸"）
    private double currentSpecGramPerUnit = 0.0; // 当前规格每单位的克数（如3.0）
    private int selectedUnitCount = 1; // 用户选择的单位数量（1、2、3）
    
    // 胶囊剂型规格解析相关变量
    private String currentCapsuleSpecUnit = ""; // 当前胶囊规格的单位（如"粒"）
    private double currentCapsuleSpecGramPerUnit = 0.0; // 当前胶囊规格每单位的克数（如0.5）
    private int selectedCapsuleUnitCount = 3; // 用户选择的胶囊单位数量（3、6、9）
    
    // 水丸剂型规格解析相关变量
    private String currentWaterPillSpecUnit = ""; // 当前水丸规格的单位（如"丸"）
    private double currentWaterPillSpecGramPerUnit = 0.0; // 当前水丸规格每单位的克数（如3.0）
    private int selectedWaterPillUnitCount = 1; // 用户选择的水丸单位数量（1、2、3）
    
    // 膏方剂型规格解析相关变量
    private String currentCreamFormulaSpecUnit = ""; // 当前膏方规格的单位（如"勺"）
    private double currentCreamFormulaSpecGramPerUnit = 0.0; // 当前膏方规格每单位的克数（如10.0）
    private int selectedCreamFormulaUnitCount = 1; // 用户选择的膏方单位数量（1、2、3）
    
    // 辅料选择相关变量
    private List<String> auxiliaryMaterialList = new ArrayList<>(); // 可选择的辅料列表
    private List<String> selectedAuxiliaryMaterials = new ArrayList<>(); // 已选择的辅料列表
    private String selectedAuxiliaryMaterialText = ""; // 选择的辅料显示文本
    private boolean isNoAuxiliaryMaterial = false; // 是否选择"不添加辅料"


    public MedicationFragment() {
        // Required empty public constructor
    }


    @Override
    protected int getLayoutId() {
        return R.layout.fragment_medication;
    }

    @Override
    protected void init() {
        super.init();
        contactsDao = AppContext.getInstances().getDaoSession().getContactsDao();
        sessionDao = AppContext.getInstances().getDaoSession().getSessionDao();
        EventBusUtils.register(this);
        mContext = getActivity();
        tipsItems = new ArrayList<>();
        mPatients = new ArrayList<>();
        patientPopItems = new ArrayList<>();
        timePopItems = new ArrayList<>();
        daijianPianhaoPopItems = new ArrayList<>();
        mDosageFormList = new ArrayList<>();
        mMedicineList = new ArrayList<>();
        medicationParams = new OrderMsgBean();
        patientId = SharedPreferenceUtils.getString(mContext, PublicParams.PATIENT_USRE_ID);
        userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        showTipTimes = SharedPreferenceForeverUtils.getInt(mContext, PublicParams.SHOW_TIP_TIMES);
        isShowMedicalServiceFee = SharedPreferenceUtils.getBoolean(mContext, PublicParams.SHOW_MEDICAL_SERVICE_FEE, true);
        getTakingTimeData();//获取用药时间
        getTabooTips();//获取服药禁忌数据
        getDosageFormList();//获取剂型类表
        getMedicalFee();//获取诊费
        getMedicalServiceFeeRadio(/*patientId*/);//获取医技服务费比例
        mBottomPopWindow = new BottomPopWindow(mContext);
        mTabooTipsPopWindow = new TabooTipsPopWindow(mContext, "选择服药禁忌", tipsItems);
        setDirectionEtOnFocusChangeListener();//监听输入内容改变展示样式
        setDayAfterOnClickListener();//监听预计服用天数
        setSumTipsOnClickListener();//监听总计提示
        setBaseFeeOnClickListener();//监听基础药费提示按钮
        setAppendFeeOnClickListener();//监听附加诊费提示
        addSelectTabooTextChangedListener();//监听输入内容改变选择服药禁忌的展示样式
        setTemplateCbOnCheckedChangedListener();//监听保存模板按钮是否选中，展示不同样式
        setEditWatchListener();//设置EditText输入内容监听事件
        //药方保密按钮监听
        setPrivacyCbOnCheckedChangedListener();
        //药方剂量保密按钮监听
        setPrivaryDoseCbOnCheckedChangedListener();

        // 设置 都不选中
        btnInternalUse.setSelected(false);
        btnExternalUse.setSelected(false);
        //设置内服外用监听
        setInOrOutBtnOnClickListener();

        setYijiTips();
        //复诊单发送控制按钮
        controlSendFZD();
        //使用药房代煎控制按钮
        dcDaiJian();
        //处理缓存的用药信息
        handleCacheMsg();

        //默认显示 普通煎150ml
        daiJianPianhaoIndex = 2;
        //初始化代煎偏好popItem数据
        initDaiJianPopItem();
        showDaiJianDataOnView();
        
        // 初始化规格选择
        initSpecificationData();
        setSpecificationClickListener();
    }

    private void dcDaiJian() {
        daiJianSwitch.setThumbResource(R.drawable.thumb);
        daiJianSwitch.setTrackResource(R.drawable.track);
    }

    /**
     * 微信用户患者信息更新事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onWechatUserUpdateEvent(NettyResult nettyResult) {
        if ("0008".equals(nettyResult.getCode()) && nettyResult.getResultCode() == null) {
            // 更新微信用户信息
            Message message = nettyResult.getMessage();
            PatientMsg patientMsg = message.getContent().getUserInfo();
            updatePatientInfo(patientMsg);
        }
    }

    /**
     * 更新患者信息
     * @param patientMsg 患者信息对象
     */
    private void updatePatientInfo(PatientMsg patientMsg) {
        if (patientMsg == null || TextUtils.isEmpty(patientMsg.getUserId())) {
            return;
        }

        // 检查是否是当前选择的用户
        if (TextUtils.equals(takerId, patientMsg.getTakerId()) ||
                TextUtils.equals(takerId, patientMsg.getUserId())) {

            // 更新记录的用户信息
            takerName = patientMsg.getName();
            takerSex = patientMsg.getSex();
            takerAge = patientMsg.getAge();
            isPregnant = patientMsg.getIsPregnent();

            // 更新UI显示
            if (!TextUtils.isEmpty(takerName)) {
                if (takerName.length() <= 4) {
                    patientNameTv.setText(takerName);
                } else {
                    patientNameTv.setText((takerName.substring(0, 4)) + "…");
                }
            }

            if (!TextUtils.isEmpty(takerAge) && !"0".equals(takerAge)) {
                ageTv.setVisibility(View.VISIBLE);
                ageTv.setText(takerAge);
            } else {
                ageTv.setVisibility(View.GONE);
            }

            if (!TextUtils.isEmpty(takerSex)) {
                sexImg.setVisibility(View.VISIBLE);
                if ("1".equals(takerSex)) { // 男
                    sexImg.setImageResource(R.drawable.male_icon_white);
                    sexAgeLl.setBackgroundResource(R.drawable.sex_age_blue_bg);
                    isPregnantTv.setText("");
                } else { // 女
                    sexImg.setImageResource(R.drawable.female_icon_white);
                    sexAgeLl.setBackgroundResource(R.drawable.sex_age_red_bg);
                    if (!TextUtils.isEmpty(isPregnant)) {
                        isPregnantTv.setText("1".equals(isPregnant) ? "怀孕" : "");
                    } else {
                        isPregnantTv.setText("");
                    }
                }
            } else {
                sexImg.setVisibility(View.GONE);
                isPregnantTv.setText("");
            }

            if (TextUtils.isEmpty(takerAge) && TextUtils.isEmpty(takerSex)) {
                sexAgeLl.setBackgroundResource(R.color.br_color_white);
            }
        }
    }

    /**
     * 处理缓存的用药信息
     */
    private void handleCacheMsg() {
        //获取缓存的用药信息
        DataCache cachePresMsg = DataCacheDaoUtil.getInstance()
                .select(DataCacheType.CACHE_PRESCRIPTION_MSG, userId + "_" + patientId);
        if (cachePresMsg != null) {
            System.out.println("用药界面获取的缓存信息：" + patientId + "=============" + JSON.toJSONString(cachePresMsg));
            medicationParams = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
            if (medicationParams != null) {
                //显示缓存的信息（不包括药材信息）
                showCachePresMsg();
                //获取缓存中的相关药方信息，以便在只进入交流界面就退出（执行onStop方法）后缓存原有的药方信息
//                if (medicationParams.getPreDetailList() != null && medicationParams.getPreDetailList().size() > 0) {
//                    mMedicineList = medicationParams.getPreDetailList();
//                }
//                leftP = medicationParams.getLeftP();
//                rightP = medicationParams.getRightP();
//                mFormListBean = medicationParams.getFormList();
//                mFormDetailMsg = medicationParams.getFormDetailMsg();
            } else {
                //获取患者信息并显示患者本人信息
                initDefaultPatientInfo();
            }

        } else {
            //获取患者信息并显示患者本人信息
            initDefaultPatientInfo();
        }
    }

    /**
     * 显示重新用药/继续编辑的弹框
     */
    private void showCacheMsgDialog() {
        if (mConfirmDialog == null) {
            mConfirmDialog = ConfirmDialog.getInstance(mContext);
        }
        mConfirmDialog.setDialogContent("上次用药方案未完成，是否继续?")
                .setPositiveText("继续编辑")
                .setNavigationText("重新用药");
        mConfirmDialog.setCancelable(false);
        mConfirmDialog.setCanceledOnTouchOutside(false);
        mConfirmDialog.show();
        mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
            @Override
            public void onNavigationBtnClicked(View view) {
                DataCacheDaoUtil.getInstance().clearCache(DataCacheType.CACHE_PRESCRIPTION_MSG
                        , userId + "_" + patientId);
                mConfirmDialog.dismiss();
            }

            @Override
            public void onPositiveBtnClicked(View view) {
                //显示缓存的信息（不包括药材信息）
                showCachePresMsg();

                if (medicationParams.getPreDetailList() != null
                        && medicationParams.getPreDetailList().size() > 0) {
                    startAddMedicineActivity(medicationParams.getFormDetailMsg()
                            , medicationParams.getFormList(), medicationParams.getTakerIsPregnant()
                            , medicationParams.getLeftP(), medicationParams.getRightP()
                            , medicationParams.getPreDetailList(), true);
                    DataCacheDaoUtil.getInstance().clearCache(DataCacheType.CACHE_PRESCRIPTION_MSG
                            , userId + "_" + patientId);
                }
                mConfirmDialog.dismiss();
            }
        });
    }

    /**
     * 显示缓存的信息
     */
    private void showCachePresMsg() {
        //显示缓存中的患者信息
        if (!TextUtils.isEmpty(medicationParams.getTakerId())) {
            showChildPatientMsg();
            isSetPatient = true;
        } else {
            //获取患者信息并显示患者本人信息
            initDefaultPatientInfo();
        }
        //诊断信息
        if (!TextUtils.isEmpty(medicationParams.getDescription())) {
            diagnoseEt.setText(medicationParams.getDescription());
            diagnoseEt.setSelection(medicationParams.getDescription().length());
        }
        //服药禁忌
        if (!TextUtils.isEmpty(medicationParams.getContraindication())) {
            String tabooStr = medicationParams.getContraindication();
            cacheTaboos = tabooStr.split("，");
            selectTaboo.setText(medicationParams.getContraindication());
        }
        //缓存中用法用量的显示
        mDrugForm = medicationParams.getDrugForm();
        
        // 恢复规格信息（如果有）
        if (!TextUtils.isEmpty(medicationParams.getPackageSpec())) {
            selectedSpecification = medicationParams.getPackageSpec();
            parseSpecificationData(selectedSpecification);
        }
        
        showUsageAndDosageData(true);
        takingTimeTv.setText(medicationParams.getUseTime());
        if (TextUtils.isEmpty(medicationParams.getSendAfterDay())) {
            afterDayEt.setText("7");
        } else {
            afterDayEt.setText(medicationParams.getSendAfterDay());
        }
        //补充说明
        if (!TextUtils.isEmpty(medicationParams.getInstructions())) {
            directionEt.setText(medicationParams.getInstructions());
        }
        //保存为常用方
        if ("1".equals(medicationParams.getIsSaveTemplate())) {
            templateCb.setChecked(true);
            if (!TextUtils.isEmpty(medicationParams.getTemplateName())) {
                templateNameEt.setText(medicationParams.getTemplateName());
                templateNameEt.setSelection(medicationParams.getTemplateName().length());
            }
        }
        //药方是否向患者保密
        if ("1".equals(medicationParams.getIsSecrecy())) {
            privaryCb.setChecked(true);
        }
        //药方剂量是否向患者保密
        if ("2".equals(medicationParams.getIsSecrecy())) {
            privaryDoseCb.setChecked(true);
        }
        //按语
        if (!TextUtils.isEmpty(medicationParams.getNote())) {
            noteEt.setText(medicationParams.getNote());
            noteEt.setSelection(medicationParams.getNote().length());
        }
        //诊费
        additionalChargeEt.setText(TextUtils.isEmpty(medicationParams.getConsultationfee())
                ? "0" : medicationParams.getConsultationfee());
        medicalFeeRemark = additionalChargeEt.getText().toString().trim();
        //线下诊断
        if (medicationParams.isDiagnosed()) {
            sureDiavisiblesedCb.setChecked(true);
        }
        //是否自动发送复诊单
        if (!TextUtils.isEmpty(medicationParams.getIsAutoSend())) {
            if ("1".equals(medicationParams.getIsAutoSend())) {
                switchBtn.setChecked(true);
                mIsAutoSend = true;
            } else {
                switchBtn.setChecked(false);
                mIsAutoSend = false;
            }
        }

    }

    /**
     * 获取患者信息，不显示弹框
     */
    public void initDefaultPatientInfo() {
        isSetPatient = false;
        getPatients();
        isShowSelectPatientPop = false;
    }

    /**
     * 代煎偏好popItems初始化
     */
    private void initDaiJianPopItem(){
        //初始化一个新数组
        String[] pianhaoArray = {"浓煎50~80ML", "浓煎100ML", "普通煎150ML", "普通煎200ML"};
        for (String text : pianhaoArray) {
            PopItem popItem = new PopItem();
            popItem.setName(text);
            daijianPianhaoPopItems.add(popItem);
        }
    }

    /**
     * 代煎显示的默认数据设置
     */
    private void showDaiJianDataOnView() {
        daiJianPianhaoSelectTv.setText(daijianPianhaoPopItems.get(daiJianPianhaoIndex).getName());
    }

    /**
     * 显示临时保存的子患者的信息
     */
    private void showChildPatientMsg() {
        isPregnant = medicationParams.getTakerIsPregnant();
        takerSex = medicationParams.getTakerSex();
        takerAge = medicationParams.getTakerAge();
        takerName = medicationParams.getTakerName();
        takerId = medicationParams.getTakerId();
        //是患者本人
        if (patientId.equalsIgnoreCase(takerId)) {
            isPatientSelf.setVisibility(View.VISIBLE);
            isPatientSelf.setText("（本人）");
        } else {
            isPatientSelf.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(takerName)) {
            if (takerName.length() <= 4) {
                patientNameTv.setText(takerName);
            } else {
                patientNameTv.setText((takerName.substring(0, 4)) + "…");
            }
        }
        if (!TextUtils.isEmpty(takerAge) && !"0".equals(takerAge)) {
            ageTv.setVisibility(View.VISIBLE);
            ageTv.setText(takerAge + "");
        } else {
            ageTv.setVisibility(View.GONE);
        }
        if (!TextUtils.isEmpty(takerSex)) {
            sexImg.setVisibility(View.VISIBLE);
            if ("1".equals(takerSex)) {//男
                sexImg.setImageResource(R.drawable.male_icon_white);
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_blue_bg);
            } else {
                sexImg.setImageResource(R.drawable.female_icon_white);
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_red_bg);
                if (!TextUtils.isEmpty(isPregnant)) {
                    if ("1".equals(isPregnant)) {
                        isPregnantTv.setText("怀孕");
                    } else {//不怀孕
                        isPregnantTv.setText("");
                    }
                } else {
                    isPregnantTv.setText("");
                }
            }
        } else {
            sexImg.setVisibility(View.GONE);
            isPregnantTv.setText("");
        }


    }

    /**
     * 控制复诊单是否自动发送
     */
    private void controlSendFZD() {
        switchBtn.setThumbResource(R.drawable.thumb);
        switchBtn.setTrackResource(R.drawable.track);
        if (SharedPreferenceForeverUtils.getBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, false)) {
            switchBtn.setChecked(true);
            afterDayEt.setVisibility(View.VISIBLE);
            dayAfter.setVisibility(View.VISIBLE);
            mIsAutoSend = true;
        } else {
            switchBtn.setChecked(false);
            afterDayEt.setVisibility(View.GONE);
            dayAfter.setVisibility(View.GONE);
            mIsAutoSend = false;
        }

        switchBtn.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
//                    SharedPreferenceForeverUtils.putBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, true);
                    afterDayEt.setVisibility(View.VISIBLE);
                    dayAfter.setVisibility(View.VISIBLE);
                } else {
//                    SharedPreferenceForeverUtils.putBoolean(mContext, PublicParams.IS_AUTO_SEND_FZD, false);
                    afterDayEt.setVisibility(View.GONE);
                    dayAfter.setVisibility(View.GONE);
                }
            }
        });
    }


    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        if (isVisibleToUser) {
            //判断显示医技服务费，目前只有福建省不显示
            if (mContext == null) {
                return;
            }
            isShowMedicalServiceFee = SharedPreferenceUtils.getBoolean(mContext, PublicParams.SHOW_MEDICAL_SERVICE_FEE, true);
            if (isShowMedicalServiceFee) {
                medicalServiceRl.setVisibility(View.VISIBLE);
                LogUtils.i(MedicationFragment.class, "setUserVisibleHint    isShowMedicalServiceFee = true");
            } else {
                LogUtils.i(MedicationFragment.class, "setUserVisibleHint    isShowMedicalServiceFee = false");
                medicalServiceRl.setVisibility(View.GONE);
            }
        }
        super.setUserVisibleHint(isVisibleToUser);
    }

    /**
     * 监听保存模板按钮是否选中，展示不同样式
     */
    private void setTemplateCbOnCheckedChangedListener() {
        templateCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    templateNameEt.setVisibility(View.VISIBLE);
//                    mDottedLine.setVisibility(View.VISIBLE);
                    templateNameEt.requestFocus();
                } else {
                    templateNameEt.setVisibility(View.GONE);
//                    mDottedLine.setVisibility(View.GONE);
                }
            }
        });
    }

    /**
     * 监听输入内容改变选择服药禁忌的展示样式
     */
    private void addSelectTabooTextChangedListener() {
        selectTaboo.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (!"选择服药禁忌".equals(selectTaboo.getText().toString())) {
                    selectTaboo.setGravity(Gravity.LEFT);
                    selectTaboo.setTextColor(getResources().getColor(R.color.br_color_theme_text));

                } else {
                    selectTaboo.setGravity(Gravity.RIGHT);
                    selectTaboo.setTextColor(getResources().getColor(R.color.br_color_et_hint));
                }

            }
        });
    }

    /**
     * 监听预计服用天数问号按钮
     */
    private void setDayAfterOnClickListener() {
        if (ivMark == null) {
            return;
        }
        ivMark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("预计服用提示")
                        .setDialogContent("服用天数只是预估，最终以药房实际制作出药量为准。")
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 总计点击监听
     */
    private void setSumTipsOnClickListener() {
        if (ivSumMark == null) {
            return;
        }
        ivSumMark.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("提示信息")
                        .setDialogContent("1、患者支付订单时只显示总计费用，不显示诊费等明细。\n2、付款后24小时内顺丰快递发货(节假日、膏/丸/散/胶囊剂型除外)。\n3、方案一：基础药费(不含制作费、诊费)满100元包邮，不满100元收取15元快递费。\n  方案二：总计费用(不含制作费、诊费)满100元包邮，不满100元收取15元快递费。\n4、由于处方调剂后无法再销售，非质量问题不办理退货退款，敬请谅解！")
                        .setContentGravity(Gravity.LEFT)
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 监听基础药费提示按钮
     */
    private void setBaseFeeOnClickListener() {
        if (baseMedicineInfoIcon == null) {
            return;
        }
        baseMedicineInfoIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("基础药费提示")
                        .setDialogContent("患者支付订单后，请到“管理-->我的钱包”查看【医技服务费/诊后服务费】及【诊费】。")
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 监听附加诊费提示
     */
    private void setAppendFeeOnClickListener() {
        if (additionalChargeInfoIcon == null) {
            return;
        }
        additionalChargeInfoIcon.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final AlertDialog dialog = AlertDialog.getInstance(mContext);
                dialog.setDialogTitle("附加诊费提示")
                        .setDialogContent("患者端不会单独显示诊费，只显示总计金额。")
                        .setPositiveText("知道了")
                        .setOnPositiveBtnClickedListener(view -> {
                            dialog.dismiss();
                        });
                dialog.show();
            }
        });
    }

    /**
     * 监听输入内容改变补充说明的展示样式
     */
    private void setDirectionEtOnFocusChangeListener() {
        if (directionEt == null) {
            return;
        }
        directionEt.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (directionEt == null) {
                    return;
                }
                if (hasFocus) {
                    directionEt.setGravity(Gravity.LEFT);
                } else {
                    if (directionEt.getText().toString().length() > 0) {
                        directionEt.setGravity(Gravity.LEFT);
                    } else {
                        directionEt.setGravity(Gravity.RIGHT);
                    }
                }
            }
        });
    }

    /**
     * 监听药方向患者保密 privaryCb
     */
    private void setPrivacyCbOnCheckedChangedListener() {
        if (privaryCb == null) {
            return;
        }
        privaryCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    privaryDoseCb.setChecked(false);
                }
            }
        });
    }

    /**
     * 监听药方剂量保密 privaryDoseCb
     */
    private void setPrivaryDoseCbOnCheckedChangedListener() {
        if (privaryDoseCb == null) {
            return;
        }
        privaryDoseCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    privaryCb.setChecked(false);
                }
            }
        });
    }

    /**
     * 设置相关EditText的输入监听事件
     */
    private void setEditWatchListener() {
        //普通剂型的药费和发送问诊单时间随着"药方付数"的改变及时刷新
        addTotalDoseEtTextChangedListener();
        //普通剂型的发送问诊单时间随着"每日几剂"的改变及时刷新
        addDayDoseEtTextChangedListener();
        // 设置服用天数、特殊剂型发送问诊单时间随着"每天多少次"的改变及时刷新
        addPreDayDoseEtTextChangedListener();
        // 设置服用天数、特殊剂型发送问诊单时间随着"每次多少g"的改变及时刷新
        addPreTimeDoseEtTextChangedListener();
        //设置总计随着诊费的改变及时刷新
        addAddtionalChargeEtTextChangedListener();
        //设置医技服务费的改变而影响的总药费、医技服务费新比例及总计的改变
        addDoctorServiceFeeEtTextChangesListener();
        doctorServiceFeeEt.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                isEditing = hasFocus;
            }
        });
    }

    /**
     * 监听内服和外用按钮
     */
    private void setInOrOutBtnOnClickListener() {
        View.OnClickListener usageMethodClickListener = v -> {
            btnInternalUse.setSelected(v.getId() == R.id.btn_internal_use);
            btnExternalUse.setSelected(v.getId() == R.id.btn_external_use);
        };

        btnInternalUse.setOnClickListener(usageMethodClickListener);
        btnExternalUse.setOnClickListener(usageMethodClickListener);
    }

    /**
     * 设置医技服务费提示语
     */
    private void setYijiTips() {
        if (showTipTimes >= 3) {
            return;
        }
        mLayoutRoot.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
            @Override
            public void onLayoutChange(View v, int left, int top, int right, final int bottom, int oldLeft, int oldTop, int oldRight, int oldBottom) {
                LogUtils.e(MedicationFragment.class, "onLayout:showTimes = " + showTipTimes +
                        ",bottom=" + bottom + ",oldBottom=" + oldBottom);

                if (isEditing && oldBottom - bottom > DensityUtils.dip2px(getActivity(), 100)) {
                    if (showTipTimes >= 3) {
                        return;
                    }
                    //医技服务费获取焦点并且弹出软键盘的情况下显示
                    if (bottomView.getVisibility() == View.GONE) {
                        mLayoutRoot.post(new Runnable() {
                            @Override
                            public void run() {
                                bottomView.setVisibility(View.VISIBLE);
                                SharedPreferenceForeverUtils.putInt(mContext, PublicParams.SHOW_TIP_TIMES, ++showTipTimes);
                            }
                        });
                    }
                } else if (bottom - oldBottom > DensityUtils.dip2px(getActivity(), 100)) {
                    //收起软键盘时隐藏
                    if (bottomView.getVisibility() == View.VISIBLE) {
                        mLayoutRoot.post(new Runnable() {
                            @Override
                            public void run() {
                                bottomView.setVisibility(View.GONE);
                            }
                        });
                    }
                }
            }
        });

        scrollView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                //当提示显示时，滑动背景隐藏提示
                if (bottomView.getVisibility() == View.VISIBLE) {
                    bottomView.setVisibility(View.GONE);
                }
                return false;
            }
        });
    }


    /**
     * 医技服务费改变后总药费、医技服务费新比例及总计实时改变
     */
    private void addDoctorServiceFeeEtTextChangesListener() {
        doctorServiceFeeEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            /**
             * @param s
             */
            @Override
            public void afterTextChanged(Editable s) {
                if (s.toString().startsWith(".")) {
                    return;
                }
                if (!TextUtils.isEmpty(mDrugForm)) {
                    if (PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
                        if (isEditing) {
                            //医技服务费新比例
                            newDoctorServiceRadio = countNewDoctorServiceRadio(false);
                        }
                        //总药费
//                        medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(false));
                        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
                        //总计
//                        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(false));
                        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));

                    } else {
                        if (isEditing) {
                            //医技服务费新比例
                            newDoctorServiceRadio = countNewDoctorServiceRadio(true);
                        }
                        //总药费
//                        medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(true));
                        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
                        //总计
//                        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(true));
                        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
                    }

                }

            }
        });
    }

    /**
     * 设置总计随着诊费的改变及时刷新
     */
    private void addAddtionalChargeEtTextChangedListener() {
        additionalChargeEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (s.toString().startsWith(".")) {
                    return;
                }
                //总计
                if (!TextUtils.isEmpty(mDrugForm)) {
                    if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)
                            || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
//                        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(false));
                        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
                    } else {
//                        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(true));
                        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
                    }
                } else {//显示默认的总计
                    olderTotalpriceTv.setText("¥ " + countDefaultPrescriptionTotalPrice());
                }
            }
        });
    }

    /**
     * 设置服用天数、特殊剂型发送问诊单时间随着"每次多少g"的改变及时刷新
     */
    private void addPreTimeDoseEtTextChangedListener() {
        pretimeDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                // 计算预计服用天数
                takeDayTv.setText(countTakeDays());
                //发送问诊单
                afterDayEt.setText(countSendWzdDays(true));

                //保存pretimeDoseEt输入的到pretimeDoseEtMap
                savePreTimeDoseEt(s.toString(), mDrugForm);

            }
        });
    }

    /**
     * 设置服用天数、特殊剂型发送问诊单时间随着"每天多少次"的改变及时刷新
     */
    private void addPreDayDoseEtTextChangedListener() {
        predayDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                // 计算预计服用天数
                takeDayTv.setText(countTakeDays());
                //发送问诊单时间
                afterDayEt.setText(countSendWzdDays(true));

                //保存preDayDose
                savePreDayDoseEt(s.toString(), mDrugForm);
            }
        });
    }

    //根据剂型，保存predayDoseEt输入的到predayDoseEtMap的方法,对应可以的前缀为 preDayDoseEt_ + 剂型
    private void savePreDayDoseEt(String et, String drugform) {
        predayDoseEtMap.put(preDayDoseKey + drugform, et);
    }

    private String getPreDayDoseEt(String drugform) {
        return predayDoseEtMap.get(preDayDoseKey + drugform);
    }
    //检查predayDoseEtMap是否包含某个剂型对应的值
    private boolean hasPreDayDoseEt(String drugform) {
        return predayDoseEtMap.containsKey(preDayDoseKey + drugform);
    }

    //根据剂型，保存pretimeDoseEt输入的到pretimeDoseEtMap的方法,对应可以的前缀为 preTimeDoseEt_ + 剂型
    private void savePreTimeDoseEt(String et, String drugform) {
        pretimeDoseEtMap.put(preTimeDoseKey + drugform, et);
    }

    private String getPreTimeDoseEt(String drugform) {
        return pretimeDoseEtMap.get(preTimeDoseKey + drugform);
    }
    //检查pretimeDoseEtMap是否包含某个剂型对应的值
    private boolean hasPreTimeDoseEt(String drugform) {
        return pretimeDoseEtMap.containsKey(preTimeDoseKey + drugform);
    }


    /**
     * 普通剂型的发送问诊单时间随着"每日几剂"的改变及时刷新
     */
    private void addDayDoseEtTextChangedListener() {
        dayDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                //发送问诊单时间
                afterDayEt.setText(countSendWzdDays(false));
            }
        });
    }

    /**
     * 普通剂型的药费和发送问诊单时间随着"药方付数"的改变及时刷新
     */
    private void addTotalDoseEtTextChangedListener() {
        totalDoseEt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {//颗粒、饮片、外用中药的总计及问诊单发送时间计算
                //基础药费
                countGeneralMedicineTotalPrice(s.toString().trim());
                //医技服务费
                doctorServiceFeeEt.setText(countDoctorServiceFee(false));
                String num = TextUtils.isEmpty(s.toString())
                        ? "7" : s.toString();//付数
                String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                        ? "¥0.00" : "¥" + medicationParams.getMakeCost();//制作费
                //制作费,代煎费
                makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" + (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));

                //总药费
//                medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(false));
                medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
                //总计
//                olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(false));
                olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
                //发送问诊单时间
                afterDayEt.setText(countSendWzdDays(false));

            }
        });
    }

    /**
     * 计算颗粒、饮片、外用中药的药材费用
     *
     * @param num 付数
     */
    private void countGeneralMedicineTotalPrice(String num) {
        String price = "0";
        String t_price = "0";
        if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getDrugPrice())) {
//            if (!isShowMedicalServiceFee) {
//                price = DecimalUtils.div(medicationParams.getDrugPrice(), PRICE_CONVERSION_RATIO, 3);
//                t_price = DecimalUtils.div(countMedicinePrice(num), PRICE_CONVERSION_RATIO);
//            } else {
            price = DecimalUtils.format(medicationParams.getDrugPrice(), 3);
            t_price = DecimalUtils.format(countMedicinePrice(num), 2);
//            }
            baseMedicinePriceTv.setText("¥" + price
                    + " * " + num + " = ¥" + t_price);
        }
    }

    /**
     * 计算药材价格
     * 付数* 单价
     *
     * @param num 付数
     * @return
     */
    private String countMedicinePrice(String num) {
        String totalDrugPrice = "0";
        String drugPrice = "0";
        if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getDrugPrice())
                && !TextUtils.isEmpty(num)) {
            drugPrice = medicationParams.getDrugPrice();
            totalDrugPrice = DecimalUtils.multiply(drugPrice, num);
        }
        return totalDrugPrice;
    }

    /**
     * @param basePrice 基础药价
     * @param radio     换算比例
     * @return 标准药价
     */
    private String getStandardPrice(String basePrice, String radio) {
        if (!TextUtils.isEmpty(basePrice) && TextUtils.isEmpty(radio)) {
            return DecimalUtils.div(basePrice, radio);
        } else {
            return "0";
        }

    }

    /**
     * @param isSpecial 是否是特殊剂型 为true 表示是散剂、膏方、蜜丸、水丸；false 表示是颗粒、饮片、外用中药
     *                  计算发送问诊单的时间
     */
    private String countSendWzdDays(boolean isSpecial) {
        String days = "";
        int intDay = 1;
        String totalDose = TextUtils.isEmpty(totalDoseEt.getText().toString().trim())
                ? "7" : totalDoseEt.getText().toString().trim();//共几剂
        String preDayDose = TextUtils.isEmpty(dayDoseEt.getText().toString())
                ? "1" : dayDoseEt.getText().toString();//每日几剂

        if (isSpecial) {
            if (medicationParams != null) {
                String makeDays = TextUtils.isEmpty(medicationParams.getMakeDays())
                        ? "0" : medicationParams.getMakeDays();
                days = DecimalUtils.addResultIntDOWN(makeDays, countTakeDays());
            }
        } else {
            if (!TextUtils.isEmpty(totalDose) && !TextUtils.isEmpty(preDayDose) && !"0".equals(preDayDose)) {
                days = DecimalUtils.div(totalDose, preDayDose, 0, BigDecimal.ROUND_DOWN);
            } else {
                days = "0";
            }
            intDay = Integer.parseInt(days);
            if (intDay < 1) {
                days = "1";
            }

        }
        if (TextUtils.isEmpty(days)) {
            if (!TextUtils.isEmpty(totalDose)) {
                days = totalDose;
            } else {
                days = "7";
            }
        }
        return days;
    }

    /**
     * 获取诊费
     */
    private void getMedicalFee() {
        getMedicalFeeCallBack = addHttpPostRequest(HttpUrlManager.GET_MEDICAL_FEE, null
                , ServiceSettingBean.class, this);
    }

    /**
     * 获取医技服务费比例
     */
    public void getMedicalServiceFeeRadio(/*String buyUserId*/) {
        Map map = new HashMap();
        map.put("buyUserId", patientId);
        map.put("apiVer", "1");
        getMedicalServiceFeeRadioCallBack = addHttpPostRequest(HttpUrlManager.GET_DOCTOR_SERVICEFEE_RADIO
                , map, DoctorServiceFeeRadioBean.class, this);

    }

    /**
     * 获取选则剂型列表
     */
    private void getDosageFormList() {
        Map<String, String> params = new HashMap<>();
        params.put("apiVer", "2");
        getDosageFormListCallBack = addHttpPostRequest(HttpUrlManager.DOSAGE_FORM_LIST, params
                , DosageFormBean.class, this);
    }

    /**
     * 获取服药禁忌数据
     */
    private void getTabooTips() {
        getTabooTipsCallBack = addHttpPostRequest(HttpUrlManager.TABOO_TIPS, null
                , TabooTipsBean.class, this);
    }

    /**
     * 显示添加的药材
     */
    private void showAddMedicines(FlowLayout medicineContainer, List<MedicineDetailMsgBean> medicines) {
        if (medicines != null && medicines.size() > 0) {
            addMedicinesLl.setVisibility(View.GONE);
            medicineMessLl.setVisibility(View.VISIBLE);
            medicineFormSupplier.setVisibility(View.VISIBLE);

            medicineContainer.removeAllViews();
            for (MedicineDetailMsgBean medicine : medicines) {
                TextView tv = new TextView(mContext);
                tv.setLayoutParams(new FlowLayout.LayoutParams(FlowLayout.LayoutParams.WRAP_CONTENT
                        , FlowLayout.LayoutParams.WRAP_CONTENT));
                tv.setPadding(DensityUtils.dip2px(mContext, 10)
                        , DensityUtils.dip2px(mContext, 5)
                        , DensityUtils.dip2px(mContext, 15)
                        , DensityUtils.dip2px(mContext, 5));
                tv.setTextColor(getResources().getColor(R.color.br_color_theme_text));
                tv.setTextSize(15);
//            tv.setTextSize(TypedValue.COMPLEX_UNIT_PX,DensityUtils.sp2px(mContext,15));
                String content = medicine.getDrugName() + medicine.getDose() + medicine.getUnit();
                SpannableStringBuilder ssb = new SpannableStringBuilder(content);
                if (PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)) {//饮片
                    if (!TextUtils.isEmpty(medicine.getUseMethod())) {//特殊煎药方式
                        String subContent = "(" + medicine.getUseMethod() + ")";
                        ssb.append(subContent);
                    }
                }
                ssb.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.br_color_red_ef4d3b))
                        , content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                ssb.setSpan(new AbsoluteSizeSpan(DensityUtils.sp2px(mContext, 12))
                        , content.length(), ssb.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
                tv.setText(ssb);
                medicineContainer.addView(tv);
            }
        } else {
            addMedicinesLl.setVisibility(View.VISIBLE);
            medicineMessLl.setVisibility(View.GONE);
        }

    }

    /**
     * 清空已添加药材
     */
    private void clearMedicines() {
        if (medicineContainer != null && medicineContainer.getChildCount() > 0) {
            if (mConfirmDialog == null) {
                mConfirmDialog = ConfirmDialog.getInstance(mContext);
            }
            mConfirmDialog.setNavigationText("取消")
                    .setPositiveText("删除")
                    .setDialogContent("是否删除已添加的药材列表？").show();
            mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    setDefaultViewAndData();
                    mConfirmDialog.dismiss();
                }
            });

        } else {
            ToastUtils.showShortMsg(mContext, "您还未添加药材.");
        }
    }


    @OnClick({R.id.select_patient_btn, R.id.clear_tv, R.id.add_medicines_ll, R.id.ll_edit,
            R.id.select_taboo, R.id.generate_basis_btn, R.id.select_taking_time_img,R.id.select_daijian_preference_img,
            R.id.price_detail_tv, R.id.show_price_detail_iv, R.id.auxiliary_material_ll})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            //选择开方患者
            case R.id.select_patient_btn:
                //为了及时更新患者列表
                getPatients();
                showSelectPatientsPop();
                break;
            //清空已添加药材
            case R.id.clear_tv:
                clearMedicines();
                break;
            //跳添加药材界面
            case R.id.add_medicines_ll:
                showDosageFormsPop();
                break;
            //继续编辑药材，调添加药材界面
            case R.id.ll_edit:
                startAddMedicineActivity(mFormDetailMsg, mFormListBean, isPregnant, leftP, rightP
                        , mMedicineList, false);
                break;
            //选择服药禁忌
            case R.id.select_taboo:
                showTabooTipsPop();
                break;
            //选择服用时间
            case R.id.select_taking_time_img:
                showTakingTimePop();
                break;
            //代煎偏好
            case R.id.select_daijian_preference_img:
                showDaijianPreferencePop();
                break;
            //生成医案
            case R.id.generate_basis_btn:
                doMakeMedicationCheck();
                break;
            //选择辅料
            case R.id.auxiliary_material_ll:
                showAuxiliaryMaterialPop();
                break;
            //展开收起药费明细
            case R.id.show_price_detail_iv:
            case R.id.price_detail_tv:
                if (clickCount % 2 == 0) {
                    medicinePriceLayout.setVisibility(View.VISIBLE);
                    priceDetailTv.setText("收起");
                    showPriceDetailIv.setImageResource(R.drawable.blue_up_arrows);
                } else {
                    medicinePriceLayout.setVisibility(View.GONE);
                    priceDetailTv.setText("明细");
                    showPriceDetailIv.setImageResource(R.drawable.blue_down_arrows);
                }
                clickCount++;
                break;
        }
    }

    /**
     * 跳转快速开方界面
     */
    private void toQuicklyPrescription(ArrayList<PatientMsgBean.PatientsBean> takerList, int userSource) {
        Intent intent = new Intent(getActivity(), QuicklyPrescriptionActivity.class);
        intent.putParcelableArrayListExtra("takerList", takerList);
        if (userSource == 1) {
            //微信开方
            intent.putExtra("prescriptionType", "weixin");
        } else if (userSource == 2) {
            //短信开方
            intent.putExtra("prescriptionType", "sms");
        }
        startActivity(intent);
    }

    public void showQuickOrderUserTipsLayout(PatientExpandInfoBean bean) {
        if (quickOrderUserTipsLayout != null) {
            quickOrderUserTipsLayout.setVisibility((bean != null && bean.isQuickOrderUser() && !bean.isSubscribeWx()) ? View.VISIBLE : View.GONE);
        }
        if (bean != null && goToQuickOrder != null) {
            goToQuickOrder.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    toQuicklyPrescription(bean.getTakerList(), bean.getUserSource());
                }
            });
        }
    }

    /**
     * 跳转到添加药材界面
     *
     * @param formDetailMsg
     * @param formListBean
     * @param isPregnant
     * @param leftP
     * @param rightP
     * @param list
     */
    private void startAddMedicineActivity(DosageFormBean.FormList.FormDetailMsg formDetailMsg
            , DosageFormBean.FormList formListBean, String isPregnant, int leftP, String rightP
            , List<MedicineDetailMsgBean> list, boolean isCache) {
        Intent intent = new Intent(mContext, AddMedicineActivity.class);
        intent.putExtra(PublicParams.DOSAGE_SUPPLIER_BEAN, formDetailMsg);
        intent.putExtra(PublicParams.DOSAGEFORMBEAN, formListBean);
        intent.putExtra(PublicParams.IS_PREGNANT, isPregnant);
        intent.putExtra("leftPosition", leftP);
        intent.putExtra("rightPosition", rightP);
        intent.putExtra("editMedicines", (Serializable) list);
        intent.putExtra(PublicParams.IS_CACHE_MEDICINE, isCache);
        intent.putExtra(PublicParams.TAKERID, takerId);
        intent.putExtra(PublicParams.FROM, FormAndOrderMsgBean.FROM_NORMAL_MEDICINE);
        startActivity(intent);
    }

    /**
     * 处理生成医案前的校验
     */
    private void doMakeMedicationCheck() {
        boolean isUsageAndDoseEmpty = false;
        boolean isDoseTooLarge = false;
        //辨证
        String description = diagnoseEt.getText().toString().trim();
        //共多少剂
        String totalDose = totalDoseEt.getText().toString().trim();
        //每日多少剂
        String dayDose = dayDoseEt.getText().toString().trim();
        //每剂分多少次服用
        String preDoseTime = preDoseTimeEt.getText().toString().trim();
        //每日几次
        String preDayDose = predayDoseEt.getText().toString().trim();
        //每次多少g
        String preTimeDose = pretimeDoseEt.getText().toString().trim();
        //预计服用到少天
        String takeDay = takeDayTv.getText().toString().trim();
        //复诊单时间
        String afterDay = afterDayEt.getText().toString().trim();
        String templateName = templateNameEt.getText().toString();

        // 检查颗粒剂型和必然甄选厂商的情况
        if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) && mFormDetailMsg != null && mFormDetailMsg.getName().contains("必然甄选")) {
            // 检查剂数是否小于7剂
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }

            if (intTotalDose < 7) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent("该厂商为合煎颗粒(药液浓缩后干燥)，非配方颗粒，7剂起制作(药材总量不低于800g)")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }

        // 添加：代煎剂型下，青庐药局【精品选货】厂商的总剂数最小剂数判断
        if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) && mFormDetailMsg != null && mFormDetailMsg.getName().contains("青庐药局【精品选货】")) {
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }

            if (intTotalDose < 5) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent(mFormDetailMsg.getName() + "5剂起煎，已自动修改为 5 剂。")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            // 重置总剂数为5
                            totalDoseEt.setText("5");
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }

        // 添加：代煎剂型下，除青庐药局【精品选货】厂商之外的所有厂商的最小剂数判断
        if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) && mFormDetailMsg != null && !mFormDetailMsg.getName().contains("青庐药局【精品选货】")) {
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }

            if (intTotalDose < 3) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent(mFormDetailMsg.getName() + "3剂起煎，已自动修改为 3 剂。")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            // 重置总剂数为3
                            totalDoseEt.setText("3");
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }
        
        // 添加：对于颗粒、饮片、代煎、外用中药剂型，总剂数最大值为99的限制
        if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) || 
            PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) ||
            PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) || 
            PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
            int intTotalDose = 0;
            if (!TextUtils.isEmpty(totalDose)) {
                intTotalDose = Integer.parseInt(totalDose);
            }
            
            if (intTotalDose > 99) {
                AlertDialog alertDialog = AlertDialog.getInstance(mContext);
                alertDialog.setDialogContent("总剂数最大为99，已自动修改为99剂")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(view -> {
                            // 重置总剂数为99
                            totalDoseEt.setText("99");
                            alertDialog.dismiss();
                        });
                alertDialog.show();
                return;
            }
        }

        int intTotalDose = 0;
        int intDayDose = 0;
        if (!TextUtils.isEmpty(totalDose)) {
            intTotalDose = Integer.parseInt(totalDose);
        }
        if (!TextUtils.isEmpty(dayDose)) {
            intDayDose = Integer.parseInt(dayDose);
        }
//普通剂型的用法用量判断
        if (normalUsageAndDosageLl.isShown()) {
            if (TextUtils.isEmpty(totalDose) || "0".equals(totalDose) || TextUtils.isEmpty(dayDose)
                    || "0".equals(dayDose) || TextUtils.isEmpty(preDoseTime) || "0".equals(preDoseTime)) {
                isUsageAndDoseEmpty = true;
            } else {
                isUsageAndDoseEmpty = false;
            }
        } else if (specialUsageAndDosageLl.isShown()) {
            //水丸蜜丸等的特殊剂型的用用量判断
            if (TextUtils.isEmpty(preDayDose) || "0".equals(preDayDose)
                    || TextUtils.isEmpty(preTimeDose) || "0".equals(preTimeDose)
                    || TextUtils.isEmpty(takeDay) || "0".equals(takeDay)) {
                isUsageAndDoseEmpty = true;
            } else {
                isUsageAndDoseEmpty = false;
            }
        }
        if (normalUsageAndDosageLl.isShown()) {
            //普通剂型的剂量超出判断
            if (intDayDose > intTotalDose) {
                isDoseTooLarge = true;
            } else {
                isDoseTooLarge = false;
            }
        } else if (specialUsageAndDosageLl.isShown()) {
            //水丸蜜丸等的剂量超出判断

        }
        if (mMedicineList.isEmpty()) {
            ToastUtils.showShortMsg(mContext, "请添加药材");
            return;
        }
        String checkResult = medicineCheck(mDrugForm, mMedicineList);

        if (checkResult != null) {
            ConfirmDialog confirmDialog = ConfirmDialog.getInstance(mContext);
            boolean finalIsUsageAndDoseEmpty = isUsageAndDoseEmpty;
            boolean finalIsDoseTooLarge = isDoseTooLarge;
            confirmDialog.setDialogTitle("用药提示")
                    .setDialogContent(checkResult)
                    .setPositiveText("确认开药")
                    .setNavigationText("返回修改")
                    .setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {

                        @Override
                        public void onNavigationBtnClicked(View view) {
                            confirmDialog.dismiss();
                        }

                        @Override
                        public void onPositiveBtnClicked(View view) {
                            confirmDialog.dismiss();
                            checkNext(finalIsUsageAndDoseEmpty, finalIsDoseTooLarge, description, afterDay, templateName);
                        }
                    });
            confirmDialog.show();
            return;
        }
        checkNext(isUsageAndDoseEmpty, isDoseTooLarge, description, afterDay, templateName);
    }

    private void checkNext(boolean isUsageAndDoseEmpty, boolean isDoseTooLarge, String description, String afterDay, String templateName) {
        if (TextUtils.isEmpty(description)) {
            ToastUtils.showShortMsg(mContext, "请添加辨证");
            return;
        }
        if (isUsageAndDoseEmpty) {
            ToastUtils.showShortMsg(mContext, "请添加用药用量");
            return;
        }

        if (isDoseTooLarge) {
            ToastUtils.showShortMsg(mContext, "每日剂数需小于等于总剂数");
            return;
        }

        if (usageMethodLayout.getVisibility() == View.VISIBLE) {
            if (!btnInternalUse.isSelected() && !btnExternalUse.isSelected()) {
                ToastUtils.showShortMsg(mContext, "请选择用药方法");
                return;
            }
        }

        if (switchBtn.isChecked() && (TextUtils.isEmpty(afterDay) || "0".equals(afterDay))) {
            ToastUtils.showShortMsg(mContext, "请添加复诊时间");
            return;
        }

        // 膏方剂型辅料必选检查
        if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm) && auxiliaryMaterialLl.getVisibility() == View.VISIBLE) {
            if (selectedAuxiliaryMaterials.isEmpty() && !isNoAuxiliaryMaterial) {
                ToastUtils.showShortMsg(mContext, "请选择辅料或选择不添加辅料");
                return;
            }
        }

        if (!sureDiavisiblesedCb.isChecked()) {
            ToastUtils.showShortMsg(mContext, "请确认患者已进行过线下诊断");
            return;
        }
        if (!isShowPoped && DecimalUtils.compareTo("0", doctorServiceFeeEt.getText().toString()) == 0) {
            if (tipsPop == null) {
                tipsPop = AlertDialog.getInstance(getActivity())
                        .setDialogContent("医技服务费输入为0元，表示此订单您的奖励为0元，请慎重修改！")
                        .setPositiveText("确认")
                        .setOnPositiveBtnClickedListener(new AlertDialog.OnPositiveBtnClickedListener() {
                            @Override
                            public void onPositiveBtnClicked(View view) {
                                tipsPop.dismiss();
                                isShowPoped = true;
                            }
                        });
                tipsPop.setCanceledOnTouchOutside(false);
            }
            tipsPop.show();
            return;
        }

        if (templateCb.isChecked()) {
            if (TextUtils.isEmpty(templateName)) {
                ToastUtils.showShortMsg(mContext, "请添模板名称");
            } else {
                //校验模板名称是否有重复
                checkRepeatedTemplateName(jsonToStr(templateName));
            }
        } else {
//            Intent intent = new Intent(mContext, PreviewMedicationActivity.class);
//            intent.putExtra(PublicParams.DRUG_FROM, mDrugForm);
//            intent.putExtra("orderMsgBean", setMedicationParams());
//            startActivity(intent);
            PreviewMedicationActivity.launch(mContext, mDrugForm, setMedicationParams(), "from_prescription");
        }
    }

    private String medicineCheck(String drugForm, List<MedicineDetailMsgBean> medicineList) {
//        if (!"饮片".equals(drugForm)) {
//            return null;
//        }
        if ("饮片".equals(drugForm)) {
            Pair<String, String> pair = null;
            for (MedicineDetailMsgBean bean : medicineList) {
                if (bean.getDrugName().contains("生姜")) {
                    pair = Pair.create("生姜", "生姜属于易霉药材，建议代煎或患者自备");
                }
            }
            if (pair != null) {
                return pair.second;
            }
        }
        return null;
    }

    /**
     * 检查药方名字是否有重复
     */
    private void checkRepeatedTemplateName(String data) {
        HashMap map = new HashMap();
        map.put("template", data);
        saveCommonTemplateCallBack = addHttpPostRequest(HttpUrlManager.ADD_COMMON_TEMPLATE, map, ResponseResult.class, this);

    }

    /**
     * @param name
     * @return 校验药方名称是否重复的参数
     */
    public String jsonToStr(String name) {
        String userId = SharedPreferenceUtils.getString(mContext, PublicParams.USER_ID);
        String jsonStr =
                " {" +
                        " \"templateName\":\"" + name + '\"' +
                        ", \"list\":" + new ArrayList<>() +
                        ", \"userId\":\"" + userId + '\"' +
                        ", \"templateId\":\"\"" +
                        '}';
        return jsonStr;

    }

    /**
     * 确定退出
     * true 直接退出
     * false 存值并退出
     *
     * @return
     */
    public boolean isSureExit() {
        if (diagnoseEt == null || noteEt == null || templateNameEt == null
                || directionEt == null || selectTaboo == null) {
            return false;
        } else {
            if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getTakerId())
                    && !patientId.equalsIgnoreCase(medicationParams.getTakerId())) {
                return false;
            }
            if (!TextUtils.isEmpty(diagnoseEt.getText().toString().trim())) {
                return false;
            }
            if (mMedicineList.size() > 0) {
                return false;
            }
            if (!TextUtils.isEmpty(noteEt.getText().toString().trim())) {
                return false;
            }
            if (!TextUtils.isEmpty(templateNameEt.getText().toString().trim())) {
                return false;
            }
            if (!TextUtils.isEmpty(directionEt.getText().toString().trim())) {
                return false;
            }
            if (!"选择服药禁忌".equals(selectTaboo.getText().toString().trim())) {
                return false;
            }
            if (!"饭后一小时".equals(takingTimeTv.getText().toString().trim())) {
                return false;
            }
            if (!"7".equals(totalDoseEt.getText().toString().trim())) {
                return false;
            }
            if (!"1".equals(dayDoseEt.getText().toString().trim())) {
                return false;
            }
            if (!"2".equals(preDoseTimeEt.getText().toString().trim())) {
                return false;
            }
            if (!"7".equals(afterDayEt.getText().toString().trim())) {
                return false;
            }
            if (!TextUtils.isEmpty(medicalFeeRemark) &&
                    !medicalFeeRemark.equals(additionalChargeEt.getText().toString().trim())) {
                return false;
            }
            if (templateCb.isChecked()) {
                return false;
            }
            if (privaryCb.isChecked()) {
                return false;
            }
            if (privaryDoseCb.isChecked()){
                return false;
            }
            if (sureDiavisiblesedCb.isChecked()) {
                return false;
            }
            if (mIsAutoSend != switchBtn.isChecked()) {
                return false;
            }

        }
        return true;
    }

    /**
     * 设置用药参数
     */
    private OrderMsgBean setMedicationParams() {
        OrderMsgBean cacheOrderMsgBean = null;
        DataCache cachePresMsg = DataCacheDaoUtil.getInstance()
                .select(DataCacheType.CACHE_PRESCRIPTION_MSG, userId + "_" + patientId);
        if (cachePresMsg != null) {
            try {
                cacheOrderMsgBean = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (medicationParams != null) {
            medicationParams.setCode(null);
            medicationParams.setErrorMsg(null);
            if (cacheOrderMsgBean != null && !TextUtils.isEmpty(takerId) && takerId.equalsIgnoreCase(cacheOrderMsgBean.getTakerId())) {
                //服药者姓名
                medicationParams.setTakerName(cacheOrderMsgBean.getTakerName());
                //服药者年龄
                medicationParams.setTakerAge(cacheOrderMsgBean.getTakerAge());
                //服药者id
                medicationParams.setTakerId(cacheOrderMsgBean.getTakerId());
                //服药者是否怀孕
                medicationParams.setTakerIsPregnant(cacheOrderMsgBean.getTakerIsPregnant());
                //服药者性别
                medicationParams.setTakerSex(cacheOrderMsgBean.getTakerSex());
            } else {
                //服药者姓名
                medicationParams.setTakerName(takerName);
                //服药者年龄
                medicationParams.setTakerAge(takerAge);
                //服药者id
                medicationParams.setTakerId(takerId);
                //服药者是否怀孕
                medicationParams.setTakerIsPregnant(isPregnant);
                //服药者性别
                medicationParams.setTakerSex(takerSex);
            }
            //患者id
            medicationParams.setPatientId(patientId);
            //辨证、病情描述
            medicationParams.setDescription(TextUtils.isEmpty(diagnoseEt.getText().toString().trim())
                    ? "" : diagnoseEt.getText().toString().trim());
            //用药时间"饭后一小时",
            medicationParams.setUseTime(TextUtils.isEmpty(takingTimeTv.getText().toString().trim())
                    ? "" : takingTimeTv.getText().toString().trim());
            //发送复诊单时间
            medicationParams.setSendAfterDay(TextUtils.isEmpty(afterDayEt.getText().toString())
                    ? "" : afterDayEt.getText().toString());
            //服药禁忌
            medicationParams.setContraindication("选择服药禁忌".equals(selectTaboo.getText().toString().trim())
                    ? "" : selectTaboo.getText().toString().trim());
            //补充说明
            medicationParams.setInstructions(TextUtils.isEmpty(directionEt.getText().toString().trim())
                    ? "" : directionEt.getText().toString().trim());
            //  模板名称
            if (templateCb != null && templateCb.isChecked()) {
                medicationParams.setIsSaveTemplate("1");
                medicationParams.setTemplateName(TextUtils.isEmpty(templateNameEt.getText().toString().trim())
                        ? "" : templateNameEt.getText().toString().trim());
            } else {
                medicationParams.setIsSaveTemplate("0");
                medicationParams.setTemplateName("");
            }
            //是否对患者保密
            if (privaryCb != null && privaryCb.isChecked()) {
                medicationParams.setIsSecrecy("1");
            }
            else if (privaryDoseCb != null && privaryDoseCb.isChecked()) {
                medicationParams.setIsSecrecy("2");
            }
            else {
                medicationParams.setIsSecrecy("0");
            }
            //是否自动发送问诊单
            if (switchBtn.isChecked()) {
                medicationParams.setIsAutoSend("1");
            } else {
                medicationParams.setIsAutoSend("0");
            }
            //设置医技服务费
            if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
                medicationParams.setMedicalServiceFee(countDoctorServiceFeeOriginal(false));
            } else {
                medicationParams.setMedicalServiceFee(countDoctorServiceFeeOriginal(true));
            }
            //特殊用法用量
            //总重
            medicationParams.setBalanceWeight(specialTotalDoseTv.getText().toString()
                    .replace("g", "").trim());
            //每日几次
            medicationParams.setMrjc(predayDoseEt.getText().toString());
            //每次几g
            if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm) && !TextUtils.isEmpty(currentSpecUnit) 
                    && currentSpecGramPerUnit > 0 && selectedUnitCount > 0) {
                // 蜜丸剂型有规格时，mcjk保存的是计算后的克数
                double totalGram = selectedUnitCount * currentSpecGramPerUnit;
                medicationParams.setMcjk(String.valueOf(totalGram));
                medicationParams.setPillUnitCount(String.valueOf(selectedUnitCount)); // 本地缓存使用
                medicationParams.setPackageSpec(selectedSpecification); // 提交服务器使用
            } else if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm) && !TextUtils.isEmpty(currentCapsuleSpecUnit) 
                    && currentCapsuleSpecGramPerUnit > 0 && selectedCapsuleUnitCount > 0) {
                // 胶囊剂型有规格时，mcjk保存的是计算后的克数
                double totalGram = selectedCapsuleUnitCount * currentCapsuleSpecGramPerUnit;
                medicationParams.setMcjk(String.valueOf(totalGram));
                medicationParams.setCapsuleUnitCount(String.valueOf(selectedCapsuleUnitCount)); // 本地缓存使用
                medicationParams.setCapsulePackageSpec(selectedSpecification); // 提交服务器使用
            } else if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm) && !TextUtils.isEmpty(currentWaterPillSpecUnit) 
                    && currentWaterPillSpecGramPerUnit > 0 && selectedWaterPillUnitCount > 0) {
                // 水丸剂型有规格时，mcjk保存的是计算后的克数
                double totalGram = selectedWaterPillUnitCount * currentWaterPillSpecGramPerUnit;
                medicationParams.setMcjk(String.valueOf(totalGram));
                medicationParams.setWaterPillUnitCount(String.valueOf(selectedWaterPillUnitCount)); // 本地缓存使用
                medicationParams.setWaterPillPackageSpec(selectedSpecification); // 提交服务器使用
            } else if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm) && !TextUtils.isEmpty(currentCreamFormulaSpecUnit) 
                    && currentCreamFormulaSpecGramPerUnit > 0 && selectedCreamFormulaUnitCount > 0) {
                // 膏方剂型有规格时，根据单位类型处理
                if ("瓶".equals(currentCreamFormulaSpecUnit)) {
                    // 单位为"瓶"时，保持原有逻辑，直接保存输入框的克数
                    medicationParams.setMcjk(pretimeDoseEt.getText().toString());
                    medicationParams.setCreamFormulaPackageSpec(selectedSpecification); // 提交服务器使用
                    medicationParams.setCreamFormulaUnitCount(""); // 清空单位数量
                } else {
                    // 其他单位时，mcjk保存的是计算后的克数
                    double totalGram = selectedCreamFormulaUnitCount * currentCreamFormulaSpecGramPerUnit;
                    medicationParams.setMcjk(String.valueOf(totalGram));
                    medicationParams.setCreamFormulaUnitCount(String.valueOf(selectedCreamFormulaUnitCount)); // 本地缓存使用
                    medicationParams.setCreamFormulaPackageSpec(selectedSpecification); // 提交服务器使用
                }
                
                // 膏方剂型保存辅料选择
                if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm)) {
                    if (isNoAuxiliaryMaterial) {
                        medicationParams.setMakeMaterial("不添加辅料");
                    } else if (!selectedAuxiliaryMaterials.isEmpty()) {
                        medicationParams.setMakeMaterial(TextUtils.join(",", selectedAuxiliaryMaterials));
                    } else {
                        medicationParams.setMakeMaterial("");
                    }
                }
            } else if (PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm) && !TextUtils.isEmpty(selectedSpecification)) {
                // 散剂剂型有规格时，将规格信息提交到packageSpec字段
                medicationParams.setMcjk(pretimeDoseEt.getText().toString()); // 正常保存输入框的克数
                medicationParams.setPackageSpec(selectedSpecification); // 提交规格到服务器
            } else {
                // 其他情况正常保存输入框的值
                medicationParams.setMcjk(pretimeDoseEt.getText().toString());
                medicationParams.setPackageSpec(""); // 清空规格字段
                // 清空其他剂型的规格字段
                medicationParams.setCapsulePackageSpec("");
                medicationParams.setWaterPillPackageSpec("");
                medicationParams.setCreamFormulaPackageSpec("");
            }
            //预计可用多少天
            medicationParams.setTakeDays(takeDayTv.getText().toString());
            //饮片颗粒外用中药的用法用量
            //共几剂
            medicationParams.setTotalPreNum(totalDoseEt.getText().toString());
            //每日几剂
            medicationParams.setDayPreNum(dayDoseEt.getText().toString());
            //每剂分几次服用
            medicationParams.setPreTimes(preDoseTimeEt.getText().toString());

            //服药方式 只有显示的时候才进行赋值
            if (usageMethodLayout.getVisibility() == View.VISIBLE) {
                medicationParams.setMode(btnInternalUse.isSelected() ?
                        btnInternalUse.getText().toString() :
                        btnExternalUse.getText().toString()
                );
            }

            //按语
            medicationParams.setNote(TextUtils.isEmpty(noteEt.getText().toString().trim())
                    ? "" : noteEt.getText().toString().trim());
            //  诊费
            medicationParams.setConsultationfee(TextUtils.isEmpty(additionalChargeEt.getText().toString().trim())
                    ? "0" : additionalChargeEt.getText().toString().trim());
            //总计
            medicationParams.setTotalPrice(TextUtils.isEmpty(olderTotalpriceTv.getText().toString().trim())
                    ? "0.00" : olderTotalpriceTv.getText().toString().trim());
            //药材列表
            medicationParams.setPreDetailList(mMedicineList);
            medicationParams.setLeftP(leftP);
            medicationParams.setRightP(rightP);
            medicationParams.setFormList(mFormListBean);
            medicationParams.setFormDetailMsg(mFormDetailMsg);
            if (sureDiavisiblesedCb.isChecked()) {
                medicationParams.setDiagnosed(true);
            } else {
                medicationParams.setDiagnosed(false);
            }
            //如果是代煎
            if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)) {
                //代煎偏好
                String preference = daijianPianhaoPopItems.get(daiJianPianhaoIndex).getName();
                medicationParams.setMakeMethod(preference);

                LogUtils.i(MedicationFragment.class, "数据设置==代煎偏好:" + preference);
            } else {
                //不是代煎的时候，代煎偏好传空
                medicationParams.setMakeMethod("");
            }

            //如果是胶囊
            if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm)){
                //当前选择的厂商中获取信息
                if (mFormDetailMsg != null){
                    String packageUnit = mFormDetailMsg.getPackageUnit(); //颗
                    
                    // 使用新的胶囊用量数量（capsuleUnitCount）作为提交参数
                    String capsuleUnitCount = medicationParams.getCapsuleUnitCount();
                    if (TextUtils.isEmpty(capsuleUnitCount)) {
                        capsuleUnitCount = "3"; // 默认值
                    }

                    //设置数量和单位
                    medicationParams.setMcDose(capsuleUnitCount);
                    medicationParams.setMcDoseUnit(packageUnit);

                    LogUtils.i(MedicationFragment.class, "数据设置==胶囊用量数量:" + capsuleUnitCount + "  单位:" + packageUnit);
                }

                //胶囊的话，设置 preTimes 和 totalPreNum 为空
                medicationParams.setPreTimes("");
                medicationParams.setTotalPreNum("");
            }

            //如果是蜜丸
            if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)){
                //当前选择的厂商中获取信息且有规格数据时
                if (mFormDetailMsg != null && !TextUtils.isEmpty(selectedSpecification)){
                    String packageUnit = currentSpecUnit; // 丸
                    
                    // 使用蜜丸用量数量（pillUnitCount）作为提交参数
                    String pillUnitCount = medicationParams.getPillUnitCount();
                    if (TextUtils.isEmpty(pillUnitCount)) {
                        pillUnitCount = "1"; // 默认值
                    }

                    //设置数量和单位
                    medicationParams.setMcDose(pillUnitCount);
                    medicationParams.setMcDoseUnit(packageUnit);

                    LogUtils.i(MedicationFragment.class, "数据设置==蜜丸用量数量:" + pillUnitCount + "  单位:" + packageUnit);
                }

                //蜜丸的话，设置 preTimes 和 totalPreNum 为空
                medicationParams.setPreTimes("");
                medicationParams.setTotalPreNum("");
            }

            //如果是水丸
            if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)){
                //当前选择的厂商中获取信息且有规格数据时
                if (mFormDetailMsg != null && !TextUtils.isEmpty(selectedSpecification)){
                    String packageUnit = currentWaterPillSpecUnit; // 丸
                    
                    // 使用水丸用量数量（waterPillUnitCount）作为提交参数
                    String waterPillUnitCount = medicationParams.getWaterPillUnitCount();
                    if (TextUtils.isEmpty(waterPillUnitCount)) {
                        waterPillUnitCount = "1"; // 默认值
                    }

                    //设置数量和单位
                    medicationParams.setMcDose(waterPillUnitCount);
                    medicationParams.setMcDoseUnit(packageUnit);

                    LogUtils.i(MedicationFragment.class, "数据设置==水丸用量数量:" + waterPillUnitCount + "  单位:" + packageUnit);
                }

                //水丸的话，设置 preTimes 和 totalPreNum 为空
                medicationParams.setPreTimes("");
                medicationParams.setTotalPreNum("");
            }

            // 添加: 处理颗粒和外用中药的制作费
            if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) ||
                    PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {

                // 检查是否有显示制作费
                if (makeMedicinePriceLl.getVisibility() == View.VISIBLE) {
                    // 获取显示的制作费 (格式为: "¥x * y = ¥z")
                    String makeFeeText = makeMedicinePriceTv.getText().toString();
                    if (!TextUtils.isEmpty(makeFeeText)) {
                        try {
                            // 提取总制作费 (格式: "¥x * y = ¥z" 中的z)
                            String totalMakeFee = makeFeeText.substring(makeFeeText.lastIndexOf("¥") + 1).trim();
                            // 设置总制作费到提交参数中
                            medicationParams.setMakeCost(totalMakeFee);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                } else {
                    // 没有显示制作费时，设置为0
                    medicationParams.setMakeCost("0");
                }
            }

            return medicationParams;
        } else {
            return new OrderMsgBean();
        }
    }

    /**
     * 显示服药禁忌弹框
     */
    private void showTabooTipsPop() {
        if (tipsItems != null && tipsItems.size() > 0) {
            showTipsPop = false;
            mTabooTipsPopWindow.initPopContentData(tipsItems);
            mTabooTipsPopWindow.showPopFromBottom(mLayoutRoot);
            mTabooTipsPopWindow.OnSureBtnClickListener(new TabooTipsPopWindow.OnSureBtnClickListener() {
                @Override
                public void onSureBtnClick(String str) {
                    selectTaboo.setText(str);
                    mTabooTipsPopWindow.dimissPop();
                }
            });
        } else {
            showTipsPop = true;
            getTabooTips();
        }
    }


    /**
     * 显示选择开方患者弹框
     */
    private void showSelectPatientsPop() {
        if (patientPopItems != null && patientPopItems.size() > 0) {
            // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(patientPopItems);
            mBottomPopWindow.setPopTitle("选择开方患者");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewVisible();
            mBottomPopWindow.setBottomText("新增患者");
            mBottomPopWindow.setBottomIcon(R.drawable.white_head_icon);
            mBottomPopWindow.setDeleteEnable(true);
            mBottomPopWindow.setOnItemLongClickListener((position, popItem) -> {
                if (popItem.isMark()) {
                    ToastUtils.showShortMsg(requireContext(), "患者家属信息医生可修改，患者本人信息需要医生告知患者在必然甘草公众号中修改");
                } else {
                    Intent intent = new Intent(mContext, AddPatientActivity.class);
                    intent.putExtra("patientInfo", popItem);
                    intent.putExtra("isEdit", true);
                    startActivityForResult(intent, REQUESTCODE_EDIT);
                    mBottomPopWindow.dimissPop();
                }
            });
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, final PopItem popItem) {
                    checkIsClearData(popItem);
                    mBottomPopWindow.dimissPop();
                }
            });
            mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                    if (isDelete) {
                        if (selectedItems == null || selectedItems.isEmpty()) {
                            ToastUtils.showShortMsg(requireContext(), "您没有选择患者，不能删除！");
                            return;
                        }
                        ConfirmDialog dialog = ConfirmDialog.getInstance(requireContext()).setDialogContent("确认删除选择的患者？");
                        dialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                            @Override
                            public void onNavigationBtnClicked(View view) {
                                dialog.dismiss();
                            }

                            @Override
                            public void onPositiveBtnClicked(View view) {
                                confirmDelete(selectedItems);
                                dialog.dismiss();
                            }
                        });
                        dialog.show();
                    } else {
                        Intent intent = new Intent(mContext, AddPatientActivity.class);
                        startActivityForResult(intent, REQUESTCODE);
                        mBottomPopWindow.dimissPop();
                    }

                }
            });
        } else {
            getPatients();
        }

    }

    private void confirmDelete(List<PopItem> selectedItems) {
        List<String> ids = new ArrayList<>();
        for (PopItem item : selectedItems) {
            ids.add(item.getId());
        }
        HashMap map = new HashMap();
        map.put("patientId", patientId);
        map.put("takerIdList", JSON.toJSONString(ids));
        getPatientsCallBack = addHttpPostRequest(HttpUrlManager.DELETE_TAKER_PATIENT, map
                , ResponseResult.class, this);
    }

    /**
     * @param popItem 患者信息bean
     *                判断是否清空已添加数据
     */
    public void checkIsClearData(final PopItem popItem) {
        if (mMedicineList.size() > 0 && !isPregnant.equals(popItem.getIsPregnant())) {
            //已添加了药材且所选择患者和显示的患者是的怀孕状态不一，给出提示
            if (mConfirmDialog == null) {
                mConfirmDialog = ConfirmDialog.getInstance(mContext);
            }
            mConfirmDialog.setDialogContent("切换怀孕状态，已填用药信息将被清空")
                    .setPositiveText("确认")
                    .setNavigationText("取消")
                    .show();
            mConfirmDialog.setOnBtnClickedListener(new ConfirmDialog.OnButtonClickedListener() {
                @Override
                public void onNavigationBtnClicked(View view) {
                    mConfirmDialog.dismiss();
                }

                @Override
                public void onPositiveBtnClicked(View view) {
                    clearData();
                    mConfirmDialog.dismiss();
                    setPatientMsg(popItem, true);
                }
            });
        } else {
            setPatientMsg(popItem, true);
        }
    }

    /**
     * 设置患者信息
     *
     * @param popItem
     */
    public void setPatientMsg(PopItem popItem) {
        LogUtils.i( MedicationFragment.class,"调用 setPatientMsg: " + JSON.toJSONString(popItem));
        isPregnant = popItem.getIsPregnant();
        takerSex = popItem.getSex();
        takerAge = popItem.getAge();
        takerName = popItem.getName();
        takerId = popItem.getId();
        if (takerName.length() <= 4) {
            patientNameTv.setText(popItem.getName());
        } else {
            patientNameTv.setText((popItem.getName().substring(0, 4)) + "…");
        }

        if (!TextUtils.isEmpty(popItem.getSex())) {
            sexImg.setVisibility(View.VISIBLE);
            //"1"是男
            if ("1".equals(popItem.getSex())) {
                sexImg.setImageResource(R.drawable.male_icon_white);
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_blue_bg);
                isPregnantTv.setText("");
            } else {
                sexImg.setImageResource(R.drawable.female_icon_white);
                sexAgeLl.setBackgroundResource(R.drawable.sex_age_red_bg);
                if (!TextUtils.isEmpty(popItem.getIsPregnant())) {
                    if ("1".equals(popItem.getIsPregnant())) {
                        isPregnantTv.setText("怀孕");
                    } else {//不怀孕
                        isPregnantTv.setText("");
                    }
                } else {
                    isPregnantTv.setText("");
                }
            }
        } else {
            sexImg.setVisibility(View.GONE);
            isPregnantTv.setText("");
        }

        if (!TextUtils.isEmpty(popItem.getAge()) && !"0".equals(popItem.getAge())) {
            ageTv.setVisibility(View.VISIBLE);
            ageTv.setText(popItem.getAge() + "");
        } else {
            ageTv.setVisibility(View.GONE);
        }
        if (TextUtils.isEmpty(popItem.getAge()) && TextUtils.isEmpty(popItem.getSex())) {
            sexAgeLl.setBackgroundResource(R.color.br_color_white);
        }
        if (popItem.isMark()) {
            isPatientSelf.setVisibility(View.VISIBLE);
            isPatientSelf.setText("（本人）");
        } else {
            isPatientSelf.setVisibility(View.GONE);
        }
    }

    public void setPatientMsg(PopItem popItem, boolean isSyncData) {
        setPatientMsg(popItem);
        if (isSyncData) {
            LogUtils.i(MedicationFragment.class, "用药界面患者更改，同步患者数据到医案");
            PatientChooseChangeEvent event = new PatientChooseChangeEvent(
                    popItem.isMark(),
                    popItem.getId(),
                    popItem.getAge(),
                    popItem.getBirthday(),
                    popItem.getIsPregnant(),
                    popItem.getSex(),
                    popItem.getName()
            );
            EventBusUtils.post(event);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPatientChooseChange(PatientChooseChangeEvent event){
        PopItem popItem = new PopItem();
        popItem.setMark(event.isSelf());
        popItem.setId(event.getId());
        popItem.setAge(event.getAge());
        popItem.setSex(event.getSex());
        popItem.setIsPregnant(event.getIsPregnant());
        popItem.setName(event.getName());
        popItem.setBirthday(event.getBirthday());
        setPatientMsg(popItem, false);
    }

    /**
     * 切换服药者时清空所填的信息
     */
    private void clearData() {
        try {
            setDefaultViewAndData();
            diagnoseEt.setText("");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 成功开出药方以后清空所有的信息
     */
    public void clearAllData() {
        clearData();
    }

    /**
     * 除辨证以外都恢复默认设置
     * 显示默认设置
     */
    private void setDefaultViewAndData() {
        if (!mMedicineList.isEmpty()) {
            mMedicineList.clear();
        }
        medicineContainer.removeAllViews();
        medicationParams = new OrderMsgBean();
        medicineMessLl.setVisibility(View.GONE);
        addMedicinesLl.setVisibility(View.VISIBLE);

        //显示默认用法用量布局
        showUsageAndDosageUi(false);
        totalDoseEt.setText("7");
        dayDoseEt.setText("1");
        preDoseTimeEt.setText("2");
        afterDayEt.setText("7");
        takingTimeTv.setText("饭后一小时");
        selectTaboo.setGravity(Gravity.RIGHT);
        selectTaboo.setText("选择服药禁忌");
        directionEt.setText("");
        directionEt.setGravity(Gravity.RIGHT);
        noteEt.setText("");
        templateNameEt.setText("");
        templateNameEt.setVisibility(View.GONE);
//        mDottedLine.setVisibility(View.GONE);
        templateCb.setChecked(false);
        sureDiavisiblesedCb.setChecked(false);
        privaryCb.setChecked(false);
        privaryDoseCb.setChecked(false);
        //诊费
        additionalChargeEt.setText(TextUtils.isEmpty(medicineFee) ? "0" : medicineFee);
        //订单总额
        olderTotalpriceTv.setText("¥ " + countDefaultPrescriptionTotalPrice());
        //基础药费
        baseMedicinePriceTv.setText("¥ 0.00");
        //医技服务费
        doctorServiceFeeEt.setText("0.00");
        //药费
        medicineTotalPriceTv.setText("¥ 0.00");
        medicinePriceLayout.setVisibility(View.GONE);
        priceDetailTv.setText("明细");
        showPriceDetailIv.setImageResource(R.drawable.blue_down_arrows);
        
        // 重置规格选择
        selectedSpecification = "";
        specificationTv.setText("请选择规格");
        specificationTv.setTextColor(getResources().getColor(R.color.br_color_black_999));
        specificationItems.clear();  // 清空规格数据列表
        
        // 重置蜜丸特有的状态变量
        resetPillSpecificState();
        
        diagnoseEt.requestFocus();
        diagnoseEt.setSelection(diagnoseEt.getText().length());
    }


    /**
     * 显示选择用药时间弹框
     */
    private void showTakingTimePop() {
        if (timePopItems != null && timePopItems.size() > 0) {
            showTimePop = false;
            // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(timePopItems);
            mBottomPopWindow.setPopTitle("选择用药时间");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewVisible();
            mBottomPopWindow.setBottomText("自定义");
            mBottomPopWindow.setBottomIcon(R.drawable.icon_camera);
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    takingTimeTv.setText(popItem.getName());
                    mBottomPopWindow.dimissPop();
                }
            });
            mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                    //自定义特殊煎法
                    showCustomBoilWayDialog();
                }
            });
        } else {
            showTimePop = true;
            getTakingTimeData();
        }

    }

    /**
     * 显示代煎偏好弹窗
     */
    void showDaijianPreferencePop() {
        if (daijianPianhaoPopItems != null && daijianPianhaoPopItems.size() > 0) {
            // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(daijianPianhaoPopItems);
            mBottomPopWindow.setPopTitle("选择代煎偏好");
            mBottomPopWindow.showPop();

            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    daiJianPianhaoSelectTv.setText(popItem.getName());
                    daiJianPianhaoIndex = position;
                    mBottomPopWindow.dimissPop();
                }
            });
        }
    }
    /**
     * 自定义特殊煎法
     */
    private void showCustomBoilWayDialog() {
        mInputDialog = InputDialog.getInstance(mContext);
        mInputDialog.setInputTitle("自定义用药时间");
        mInputDialog.setInputHint("请输入用药时间");
        mInputDialog.show();
        mInputDialog.setOnButtonClickListener(new OnButtonClickListener() {
            @Override
            public void onPositiveClick(View view, CharSequence editText) {
                if (TextUtils.isEmpty(editText.toString().trim())) {
                    ToastUtils.showShortMsg(mContext, "请输入用药时间！");
                } else if (!stringFilter(editText.toString())) {
                    ToastUtils.showShortMsg(mContext, "只支持输入汉字、数字");
                } else {
                    addCustomTakingTime(editText.toString().trim());
                }
            }

            @Override
            public void onNegativeClick(View view, CharSequence editText) {
                mInputDialog.dismiss();
            }
        });
    }

    /**
     * 限制输入框只输入汉字，数字
     *
     * @param str
     * @return
     * @throws PatternSyntaxException
     */
    public boolean stringFilter(String str) throws PatternSyntaxException {
        // 只允许数字和汉字,空格
        String regEx = "[\\d\u4E00-\u9FA5\\s]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    /**
     * @param name 添加自定义用药时间
     */
    private void addCustomTakingTime(String name) {
        HashMap map = new HashMap();
        map.put("title", name);
        addCustomTakingTimeCallBack = addHttpPostRequest(HttpUrlManager.ADD_TAKING_TIME, map,
                ResponseResult.class, this);

    }

    /**
     * 显示选择剂型
     */
    private void showDosageFormsPop() {
        if (TextUtils.isEmpty(takerId)) {
            ToastUtils.showShortMsg(getActivity(), "请选择患者");
            getPatients();
            return;
        }
        mDosageFormsPop = DosageFormsPop.getInstance(mContext);
        mDosageFormsPop.isShowShortMedication(false);
        if (mDosageFormList != null && mDosageFormList.size() > 0) {
            showDosageFormPop = false;
            mDosageFormsPop.setData(mDosageFormList);
            mDosageFormsPop.show();
            mDosageFormsPop.setOnPopItemSelectedListener(new DosageFormsPop.PopItemSelectedListener() {
                @Override
                public void onPopItemSelected(DosageFormBean.FormList dosageForm
                        , DosageFormBean.FormList.FormDetailMsg formDetailMsg
                        , int leftPosition, int rightPosition) {
                    mDosageFormsPop.setLeftSelection(leftPosition);
                    mDosageFormsPop.setRightSelection(formDetailMsg.getId());
                    mFormDetailMsg = formDetailMsg;
                    mFormListBean = dosageForm;
                    // 更新厂商后重新初始化规格数据
                    initSpecificationData();
                    startAddMedicineActivity(formDetailMsg, dosageForm, isPregnant, leftPosition
                            , formDetailMsg.getId(), null, false);
                }
            });
        } else {
            showDosageFormPop = true;
            getDosageFormList();
        }

    }

    /**
     * 显示服药方式弹框
     */
    private void showTakingWayPop() {
        initTakingWays();
        // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
        mBottomPopWindow.resetState();
        mBottomPopWindow.setPopTitle("选择服药方式");
        mBottomPopWindow.setPopContentData(takingWayPopItems);
        mBottomPopWindow.showPop();
        mBottomPopWindow.setBottomViewGone();
        mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
            @Override
            public void onPopItemClick(int position, PopItem popItem) {
                mTakingWay = popItem.getName();
                mBottomPopWindow.dimissPop();

                Intent intent = new Intent(mContext, AddMedicineActivity.class);
                intent.putExtra("mMedicineType", mMedicineType);
                intent.putExtra("mTakingWay", "(" + mTakingWay + ")");
                startActivity(intent);
            }
        });
    }


    /**
     * 获取开放患者
     */
    private void getPatients() {
        LogUtils.i(MedicationFragment.class, "获取患者信息====");
        HashMap map = new HashMap();
        map.put("patientId", patientId);
        getPatientsCallBack = addHttpPostRequest(HttpUrlManager.SELECT_PATIENT, map
                , PatientMsgBean.class, this);
    }

    /**
     * 获取服药时间数据
     */
    private void getTakingTimeData() {
        getTakingTimeCallBack = addHttpPostRequest(HttpUrlManager.TAKING_TIME, null
                , TakingTimeBean.class, this);
    }

    /**
     * 初始化药材类型数据
     */
    private void initDosageFormPopData(List<DosageFormBean.FormList> formLists) {
        for (int i = 0; i < formLists.size(); i++) {
            PopItem item = new PopItem();
            item.setPosition(i);
            //剂型
            item.setName(formLists.get(i).getDrugFormName());
            typePopItems.add(item);
        }
    }

    /**
     * 初始化服药方式数据
     */
    private void initTakingWays() {
        takingWayPopItems = new ArrayList<>();
        String[] ways = new String[]{"煎汤", "外用"};
        for (int i = 0; i < ways.length; i++) {
            PopItem item = new PopItem();
            item.setPosition(i);
            item.setName(ways[i]);
            takingWayPopItems.add(item);
        }

    }


    /**
     * @param taskId 请求的任务id
     * @param result 返回数据进行解析后的对象
     */
    @Override
    public void onRequestFinished(String taskId, ResponseResult result) {
        switch (taskId) {
            case HttpUrlManager.SELECT_PATIENT:
                if (result != null && result.isRequestSuccessed()) {
                    PatientMsgBean patientMsgBean = (PatientMsgBean) result.getBodyObject();
                    if (patientMsgBean != null) {
                        mPatients.clear();
                        mPatients.addAll(patientMsgBean.getPatients());
                        if (mPatients.size() > 0) {
                            //初始化选择患者pop数据
                            initPatientsData(mPatients);

                            if (mBottomPopWindow != null && patientPopItems != null
                                    && patientPopItems.size() > 0) {
                                mBottomPopWindow.upDatePopContent(patientPopItems);
                            }
                            if (isShowSelectPatientPop) {
                                showSelectPatientsPop();
                            }
                        }
                    }

                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.TAKING_TIME:
                if (result != null && result.isRequestSuccessed()) {
                    TakingTimeBean timeBean = (TakingTimeBean) result.getBodyObject();
                    if (timeBean != null) {
                        //初始化用药时间pop数据
                        initTakingTimeData(timeBean.getMedicationTime1(), timeBean.getMedicationTime2());
                        if (timeBean.getMedicationTime1() != null && timeBean.getMedicationTime1().size() > 0
                                || (timeBean.getMedicationTime2() != null && timeBean.getMedicationTime2().size() > 0)) {
                            if (showTimePop) {
                                showTakingTimePop();
                            }
                        }
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.ADD_TAKING_TIME:
                if (result != null && result.isRequestSuccessed()) {
                    if (mInputDialog != null && mInputDialog.isShowing()) {
                        mInputDialog.dismiss();
                        getTakingTimeData();
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.TABOO_TIPS:
                if (result != null && result.isRequestSuccessed()) {
                    TabooTipsBean tipsBean = (TabooTipsBean) result.getBodyObject();
                    if (tipsBean != null) {
                        String tipsStr = tipsBean.getData();
                        if (!TextUtils.isEmpty(tipsStr)) {
                            String[] tips = tipsStr.split(";");
                            if (tips.length > 0) {
                                tipsItems.clear();
                                for (int i = 0; i < tips.length; i++) {
                                    TabooTipsBean.TipBean tipBean = new TabooTipsBean.TipBean();
                                    tipBean.setName(tips[i]);
                                    if (cacheTaboos != null && cacheTaboos.length > 0) {
                                        for (int j = 0; j < cacheTaboos.length; j++) {
                                            if (tipBean.getName().equalsIgnoreCase(cacheTaboos[j])) {
                                                tipBean.setChecked(true);
                                                break;
                                            } else {
                                                tipBean.setChecked(false);
                                            }
                                        }
                                    }
                                    tipsItems.add(tipBean);
                                }

                                if (showTipsPop) {
                                    showTabooTipsPop();
                                }
                            }

                        }
                    }


                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            //获取剂型列表
            case HttpUrlManager.DOSAGE_FORM_LIST:
                if (result != null && result.isRequestSuccessed()) {
                    DosageFormBean dosageFormBean = (DosageFormBean) result.getBodyObject();
                    if (dosageFormBean != null) {
                        mDosageFormList = (List<DosageFormBean.FormList>) dosageFormBean.getList();
                        if (mDosageFormList != null && mDosageFormList.size() > 0) {
                            if (showDosageFormPop) {
                                showDosageFormsPop();
                            }
                        }

                    }

                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            //获取诊费
            case HttpUrlManager.GET_MEDICAL_FEE:
                if (result.isRequestSuccessed()) {
                    ServiceSettingBean feeBean = (ServiceSettingBean) result.getBodyObject();
                    if (feeBean != null) {
                        medicineFee = feeBean.getChangeSetPrice();
                        //有缓存，显示缓存中的
//                        if (medicationParams != null && !TextUtils.isEmpty(medicationParams.getConsultationfee())) {
//                            additionalChargeEt.setText(medicationParams.getConsultationfee());
//                        } else {
                        additionalChargeEt.setText(TextUtils.isEmpty(medicineFee) ? "0" : medicineFee);
//                        }
                    } else {
                        additionalChargeEt.setText("0");
                    }
                    medicalFeeRemark = additionalChargeEt.getText().toString().trim();
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            //获取医技服务费比例
            case HttpUrlManager.GET_DOCTOR_SERVICEFEE_RADIO:
                if (result.isRequestSuccessed()) {
                    DoctorServiceFeeRadioBean radioBean = (DoctorServiceFeeRadioBean) result.getBodyObject();
                    if (radioBean != null) {
                        newDoctorServiceRadio = radioBean.getData();
                    } else {
                        newDoctorServiceRadio = "0.3";
                    }

                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                    newDoctorServiceRadio = "0.3";
                }
                break;
            case HttpUrlManager.ADD_COMMON_TEMPLATE://校验常用方名称
                if (result.isRequestSuccessed()) {
//                    Intent intent = new Intent(mContext, PreviewMedicationActivity.class);
//                    intent.putExtra(PublicParams.DRUG_FROM, mDrugForm);
//                    intent.putExtra("orderMsgBean", setMedicationParams());
//                    startActivity(intent);
                    PreviewMedicationActivity.launch(mContext, mDrugForm, setMedicationParams(), "from_prescription");
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            case HttpUrlManager.DELETE_TAKER_PATIENT:
                if (result.isRequestSuccessed()) {
                    getPatients();
                    ToastUtils.showShortMsg(requireContext(), "删除成功");
                    if (mBottomPopWindow != null && mBottomPopWindow.isShowing()) {
                        isShowSelectPatientPop = false;
                        mBottomPopWindow.dimissPop();
                    }
                } else {
                    RequestErrorToast.showError(mContext, taskId, result.getCode(), result.getErrorMsg());
                }
                break;
            default:
                break;

        }
    }

    /**
     * 初始化用药时间数据
     *
     * @param takingTimes       常规用药时间
     * @param customTakingTimes 自定义用药时间
     */
    private void initTakingTimeData(List<String> takingTimes, List<String> customTakingTimes) {
        timePopItems.clear();
        if (customTakingTimes != null && customTakingTimes.size() > 0) {
            for (int i = 0; i < customTakingTimes.size(); i++) {
                PopItem popItem = new PopItem();
                popItem.setName(customTakingTimes.get(i));
                timePopItems.add(popItem);
            }
        }
        if (takingTimes != null && takingTimes.size() > 0) {
            for (int i = 0; i < takingTimes.size(); i++) {
                PopItem popItem = new PopItem();
                popItem.setName(takingTimes.get(i));
                timePopItems.add(popItem);
            }
        }
        if (timePopItems != null && timePopItems.size() > 0) {
            mBottomPopWindow.upDatePopContent(timePopItems);
        }
    }

    /**
     * 初始化选择患者列表数据
     */
    private void initPatientsData(List<PatientMsgBean.PatientsBean> patients) {
        patientPopItems.clear();
        boolean takerIdIsContain = false;
        for (PatientMsgBean.PatientsBean patient : patients) {
            if (TextUtils.equals(takerId, patient.getId())) {
                takerIdIsContain = true;
                break;
            }
        }
        if (!takerIdIsContain) {
            isSetPatient = false;
        }
        if (patients != null && patients.size() > 0) {
            for (int i = 0; i < patients.size(); i++) {
                PopItem popItem = new PopItem();
                popItem.setName(patients.get(i).getName());
                popItem.setAge(patients.get(i).getAge());
                popItem.setSex(patients.get(i).getSex());
                popItem.setIsPregnant(patients.get(i).getIsPregnant());
                popItem.setId(patients.get(i).getId());
                popItem.setBirthday(patients.get(i).getBirthday());
                //isSelf字段值为"1"表示是本人
                if ("1".equals(patients.get(i).getIsSelf())) {
                    popItem.setMark(true);
                    popItem.setResId(R.drawable.is_self_icon);
                    if (!isSetPatient) {
                        setPatientMsg(popItem, true);
                        isSetPatient = true;
                    }
                    popItem.setDeleteEnable(false);
                } else {
                    popItem.setMark(false);
                }
                patientPopItems.add(popItem);
            }

        }
    }

    /**
     * 添加的药材显示处理
     *
     * @sender {@link AddMedicineActivity#onRequestFinished(String, ResponseResult) HttpUrlManager.GET_PRERSCRIPTION_PRICEMSG}
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onReceiveTemplateSelectedResult(FormAndOrderMsgBean formAndOrderMsgBean) {
        if (formAndOrderMsgBean.getFrom() != FormAndOrderMsgBean.FROM_NORMAL_MEDICINE) {
            return;
        }
        if (mMedicineList != null && mMedicineList.size() > 0) {
            mMedicineList.clear();
        }
        if (formAndOrderMsgBean != null) {


            OrderMsgBean orderMsgBean = formAndOrderMsgBean.getOrderMsgBean();
            if (medicationParams == null) {
                medicationParams = orderMsgBean;
            }
            if (medicationParams != null && orderMsgBean != null) {
                medicationParams.setDrugForm(orderMsgBean.getDrugForm());
                medicationParams.setProviderId(orderMsgBean.getProviderId());
                medicationParams.setProductSubType(orderMsgBean.getProductSubType());
                medicationParams.setTakerIsPregnant(orderMsgBean.getTakerIsPregnant());
                medicationParams.setPreDetailList(orderMsgBean.getPreDetailList());
                medicationParams.setMakeDesc(TextUtils.isEmpty(orderMsgBean.getMakeDesc()) ? "" : orderMsgBean.getMakeDesc());//制作描述
                medicationParams.setMakeCost(TextUtils.isEmpty(orderMsgBean.getMakeCost()) ? "" : orderMsgBean.getMakeCost());//制作费
                medicationParams.setBalanceWeight(TextUtils.isEmpty(orderMsgBean.getBalanceWeight()) ? "" : orderMsgBean.getBalanceWeight());//总重
                medicationParams.setMakeDays(TextUtils.isEmpty(orderMsgBean.getMakeDays()) ? "" : orderMsgBean.getMakeDays());
                if (!TextUtils.isEmpty(orderMsgBean.getDrugPrice())) {
                    medicationParams.setDrugPrice(orderMsgBean.getDrugPrice());
                }
                //方子原始总重量
                medicationParams.setWeight(TextUtils.isEmpty(orderMsgBean.getWeight()) ? "" : orderMsgBean.getWeight());
            }
            mDrugForm = formAndOrderMsgBean.getOrderMsgBean().getDrugForm();

            mFormDetailMsg = formAndOrderMsgBean.getFormDetailMsg();
            mFormListBean = formAndOrderMsgBean.getFormList();
            leftP = mFormListBean.getLeftPosition();
            rightP = mFormDetailMsg.getRightPosition();
            
            // 更新厂商信息后重新初始化规格数据
            initSpecificationData();
            List<MedicineDetailMsgBean> medicines = formAndOrderMsgBean.getOrderMsgBean().getPreDetailList();
            if (medicines != null && medicines.size() > 0) {
                mMedicineList.addAll(medicines);
                //显示添加的药材
                showAddMedicines(medicineContainer, medicines);
                System.out.println("==============添加的药材显示处理===" + mMedicineList.size() + "========");
                //显示厂商名称跟剂型
                medicineFormSupplier.setText(mDrugForm + "(" + mFormDetailMsg.getName() + ")");
                //设置医技服务费的显示
                if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
                    doctorServiceFeeEt.setText(countDoctorServiceFee(false));
                } else {
                    doctorServiceFeeEt.setText(countDoctorServiceFee(true));
                }
                //处理用法用量的显示
                showUsageAndDosageData(false);
            } else {
                setDefaultViewAndData();
            }
        }

    }

    // 判断是否是特殊剂型的辅助方法
    private boolean isSpecialDosageForm() {
        return PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm);
    }

    /**
     * 显示用法用量及制作费布局,根据剂型分类显示
     *
     * @param isCache 显示的是否是缓存中的信息
     */
    private void showUsageAndDosageData(boolean isCache) {
        //默认显示颗粒/饮片/外用中药的
        if (TextUtils.isEmpty(mDrugForm)) {
            showUsageAndDosageUi(false);
            showDoseMsgForKYExMedicine(isCache);
        } else {
            if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {
                //显示颗粒、饮片、外用中药的用法用量信息，isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForKYExMedicine(isCache);

            } else if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)
                    || PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)) {
                //显示蜜丸、水丸的用法用量信息,isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForPill(isCache);

            } else if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm)) {
                //膏方
                //显示膏方的用法用量信息,isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForCreamFormula(isCache);

            } else if (PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)) {
                //散剂
                //显示散剂的用法用量，isCache为false说明非缓存，true说明显示缓存中信息
                showDoseMsgForPowder(isCache);
            } else if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm)) {
                //胶囊用法用量
                showCapSuleMsgForPowder(isCache);
            }
        }

        //单独进行处理 饮片、代煎、散剂
        if (PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)){
            usageMethodLayout.setVisibility(View.VISIBLE);
        }else {
            usageMethodLayout.setVisibility(View.GONE);
        }

        //显示制作费布局的控制 - 修改部分
        if (medicationParams != null) {
            // 如果是特殊剂型或代煎，保持原有逻辑
            if (isSpecialDosageForm() || TextUtils.equals("代煎", medicationParams.getDrugForm())) {
                // 保持原有逻辑，不做处理
                return;
            }

            // 只针对颗粒和外用中药进行制作费显示的控制
            if (PublicParams.DOSAGEFORM_GRANULE.equals(mDrugForm) ||
                    PublicParams.DOSAGEFORM_EXTERNAL_TRADITION_MEDICINE.equals(mDrugForm)) {

                String makeCost = medicationParams.getMakeCost();
                if (!TextUtils.isEmpty(makeCost) && !"0".equals(makeCost) && !"0.00".equals(makeCost)) {
                    // 有制作费时，显示制作费布局
                    makeMedicinePriceLl.setVisibility(View.VISIBLE);
                    // 更新制作费显示
                    String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                            ? "7" : totalDoseEt.getText().toString();//付数
                    String makeFeePre = "¥" + makeCost;//单剂制作费
                    //制作费 = 单剂制作费 × 付数
                    makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" +
                            (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));
                } else {
                    // 无制作费时，隐藏制作费布局
                    makeMedicinePriceLl.setVisibility(View.GONE);
                }
            }
        }

    }

    /**
     * 显示散剂的用法用量
     *
     * @param isCache 是否显示缓存中的信息
     */
    private void showDoseMsgForPowder(boolean isCache) {
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "(" + originWeightStr + medicationParams.getMakeDesc() + ")");
        //设置pretimeDoseEt不可编辑
        pretimeDoseEt.setFocusable(true);
        pretimeDoseEt.setFocusableInTouchMode(true);
        pretimeDoseEt.setOnClickListener(null);

        //不显示胶囊颗粒数
        capsuleNumTv.setVisibility(View.GONE);
        
        // 确保散剂显示正确的单位"g"，避免受蜜丸影响
        pretimeDoseUnitTv.setText("g");
        pretimeDoseGramTv.setVisibility(View.GONE);

//        if (isCache) {
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "10" : medicationParams.getMcjk());
//        } else {
//            predayDoseEt.setText("2");
//            pretimeDoseEt.setText("10");
//        }
        if (isCache) {
            if (medicationParams != null) {
                predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "10" : medicationParams.getMcjk());
            }
        } else {
            if (hasPreDayDoseEt(mDrugForm)) {
                predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
            } else {
                predayDoseEt.setText("3");
            }
            if (hasPreTimeDoseEt(mDrugForm)) {
                pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
            } else {
                pretimeDoseEt.setText("10");
            }
        }


        //基础药费
//        if (isShowMedicalServiceFee) {
//            baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
//                    ? "¥ 0.00" : "¥ " + getMedicinePrice(true));
//        } else {
        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " + /*DecimalUtils.div(*/getMedicinePrice(true)/*, PRICE_CONVERSION_RATIO)*/);
//        }
        //总药费
//        medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(true));
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
//        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(true));
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
    }

    /**
     * 显示胶囊的用法用量
     */


    @SuppressLint("SetTextI18n")
    private void showCapSuleMsgForPowder(boolean isCache) {
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "(" + originWeightStr + medicationParams.getMakeDesc() + ")");
        //设置pretimeDoseEt不可编辑
        pretimeDoseEt.setFocusable(true);
        pretimeDoseEt.setFocusableInTouchMode(true);
        pretimeDoseEt.setOnClickListener(null);
        
        // 胶囊剂型规格处理
        if (mFormDetailMsg != null && mFormDetailMsg.getPackageSpecList() != null && !mFormDetailMsg.getPackageSpecList().isEmpty()) {
            // 如果有规格数据且已选择规格，解析胶囊规格并更新显示
            if (!TextUtils.isEmpty(selectedSpecification)) {
                parseCapsuleSpecificationData(selectedSpecification);
                
                // 从缓存中恢复胶囊单位数量选择
                if (isCache && medicationParams != null && !TextUtils.isEmpty(medicationParams.getCapsuleUnitCount())) {
                    try {
                        selectedCapsuleUnitCount = Integer.parseInt(medicationParams.getCapsuleUnitCount());
                    } catch (NumberFormatException e) {
                        selectedCapsuleUnitCount = 3; // 默认值
                    }
                }
                
                updateCapsuleDosageDisplay();
            } else {
                // 没有选择规格时，使用标准显示
                pretimeDoseUnitTv.setText("g");
                pretimeDoseGramTv.setVisibility(View.GONE);
            }
        } else {
            // 确保胶囊剂型显示正确的单位"g"，避免受蜜丸影响
            pretimeDoseUnitTv.setText("g");
            pretimeDoseGramTv.setVisibility(View.GONE);
        }
//        if (isCache) {
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "3" : medicationParams.getMcjk());
//        } else {
//            predayDoseEt.setText("2");
//            pretimeDoseEt.setText("3");
//        }

        if (isCache) {
            if (medicationParams != null) {
                predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "3" : medicationParams.getMcjk());
            }
        } else {
            if (hasPreDayDoseEt(mDrugForm)) {
                predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
            } else {
                predayDoseEt.setText("3");
            }
            if (hasPreTimeDoseEt(mDrugForm)) {
               pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
            } else {
                pretimeDoseEt.setText("3");
            }
        }

        //不显示红色颗数
        capsuleNumTv.setVisibility(View.GONE);



        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " + /*DecimalUtils.div(*/getMedicinePrice(true)/*, PRICE_CONVERSION_RATIO)*/);
        //总药费

        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
    }


    /**
     * 显示膏方的用法用量信息
     *
     * @param isCache 是否显示缓存中的信息
     */
    private void showDoseMsgForCreamFormula(boolean isCache) {
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "("+ originWeightStr + medicationParams.getMakeDesc() + ")");
        capsuleNumTv.setVisibility(View.GONE);
        
        // 检查是否有规格数据，如果有规格且已选择，则解析膏方规格并更新显示
        if (mFormDetailMsg != null && mFormDetailMsg.getPackageSpecList() != null && !mFormDetailMsg.getPackageSpecList().isEmpty() 
                && !TextUtils.isEmpty(selectedSpecification)) {
            parseCreamFormulaSpecificationData(selectedSpecification);
            
            // 从缓存中恢复膏方单位数量选择
            if (isCache && medicationParams != null && !TextUtils.isEmpty(medicationParams.getCreamFormulaUnitCount())) {
                try {
                    selectedCreamFormulaUnitCount = Integer.parseInt(medicationParams.getCreamFormulaUnitCount());
                } catch (NumberFormatException e) {
                    selectedCreamFormulaUnitCount = 1; // 默认值
                }
            }
            
            updateCreamFormulaDosageDisplay();
            
            // 特殊处理：当选择的规格单位是"瓶"时，需要设置默认的30g值
            if ("瓶".equals(currentCreamFormulaSpecUnit)) {
                // 对于"瓶"单位，保持原有的默认值显示逻辑
                if (isCache) {
                    if (medicationParams != null) {
                        pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "30" : medicationParams.getMcjk());
                    }
                } else {
                    pretimeDoseEt.setText("30");
                }
            }
        } else {
            // 确保膏方显示正确的单位"g"，避免受蜜丸影响
            pretimeDoseUnitTv.setText("g");
            pretimeDoseGramTv.setVisibility(View.GONE);
            //设置pretimeDoseEt可编辑
            pretimeDoseEt.setFocusable(true);
            pretimeDoseEt.setFocusableInTouchMode(true);
            pretimeDoseEt.setOnClickListener(null);
        }
//        if (isCache) {
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "30" : medicationParams.getMcjk());
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//        } else {
//            pretimeDoseEt.setText("30");
//            predayDoseEt.setText("2");
//        }

        // 检查是否已经通过规格单位为"瓶"的特殊处理设置了默认值
        boolean hasSetBottleUnitDefault = mFormDetailMsg != null && mFormDetailMsg.getPackageSpecList() != null && 
                !mFormDetailMsg.getPackageSpecList().isEmpty() && !TextUtils.isEmpty(selectedSpecification) &&
                "瓶".equals(currentCreamFormulaSpecUnit);
        
        if (!hasSetBottleUnitDefault) {
            // 只有在没有通过"瓶"单位特殊处理设置默认值时，才使用通用的默认值设置
            if (isCache) {
                if (medicationParams != null) {
                    pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "30" : medicationParams.getMcjk());
                    predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                }
            } else {
                if (hasPreTimeDoseEt(mDrugForm)) {
                    pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
                } else {
                    pretimeDoseEt.setText("30");
                }
                if (hasPreDayDoseEt(mDrugForm)) {
                    predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
                } else {
                    predayDoseEt.setText("3");
                }
            }
        } else {
            // 对于"瓶"单位，仍需设置每日次数的默认值
            if (isCache) {
                if (medicationParams != null) {
                    predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                }
            } else {
                if (hasPreDayDoseEt(mDrugForm)) {
                    predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
                } else {
                    predayDoseEt.setText("3");
                }
            }
        }
        //基础药费
//        if (isShowMedicalServiceFee) {
//            baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
//                    ? "¥ 0.00" : "¥ " + getMedicinePrice(true));
//        } else {
        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " + /*DecimalUtils.div(*/getMedicinePrice(true)/*, PRICE_CONVERSION_RATIO)*/);
//        }
        //总药费
//        medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(true));
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
//        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(true));
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
        
        // 显示辅料选择行（仅膏方剂型且有makeMaterialList时显示）
        if (mFormDetailMsg != null && mFormDetailMsg.getMakeMaterialList() != null && !mFormDetailMsg.getMakeMaterialList().isEmpty()) {
            auxiliaryMaterialLl.setVisibility(View.VISIBLE);
            
            if (isCache && medicationParams != null && !TextUtils.isEmpty(medicationParams.getMakeMaterial())) {
                // 从缓存恢复辅料选择
                if ("不添加辅料".equals(medicationParams.getMakeMaterial())) {
                    isNoAuxiliaryMaterial = true;
                    selectedAuxiliaryMaterials.clear();
                    auxiliaryMaterialTv.setText("不添加辅料");
                } else {
                    isNoAuxiliaryMaterial = false;
                    selectedAuxiliaryMaterials.clear();
                    String[] materials = medicationParams.getMakeMaterial().split(",");
                    for (String material : materials) {
                        if (!TextUtils.isEmpty(material.trim())) {
                            selectedAuxiliaryMaterials.add(material.trim());
                        }
                    }
                    if (!selectedAuxiliaryMaterials.isEmpty()) {
                        String displayText = TextUtils.join("、", selectedAuxiliaryMaterials);
                        auxiliaryMaterialTv.setText(displayText);
                    } else {
                        auxiliaryMaterialTv.setText("请选择辅料");
                    }
                }
            } else {
                // 默认状态
                auxiliaryMaterialTv.setText("请选择辅料");
                selectedAuxiliaryMaterials.clear();
                isNoAuxiliaryMaterial = false;
            }
        } else {
            auxiliaryMaterialLl.setVisibility(View.GONE);
        }
    }

    /**
     * 显示蜜丸、水丸的用法用量信息
     *
     * @param isCache 是否显示的是缓存中的信息
     */
    private void showDoseMsgForPill(boolean isCache) {
        //蜜丸 、水丸
        showUsageAndDosageUi(true);
        // 显示预计总重及服用天数
        specialTotalDoseTv.setText(TextUtils.isEmpty(medicationParams.getBalanceWeight())
                ? "" : medicationParams.getBalanceWeight() + "g");
        String originWeightStr = "原重"+medicationParams.getWeight()+"g,";
        //制作描述
        specialMakeDescriptionTv.setText(TextUtils.isEmpty(medicationParams.getMakeDesc())
                ? "" : "(" + originWeightStr + medicationParams.getMakeDesc() + ")");
//        if (isCache) {
//            predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "2" : medicationParams.getMrjc());
//            pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "6" : medicationParams.getMcjk());
//        } else {
//            predayDoseEt.setText("2");
//            pretimeDoseEt.setText("6");
//        }
        if (isCache) {
            if (medicationParams != null) {
                predayDoseEt.setText(TextUtils.isEmpty(medicationParams.getMrjc()) ? "3" : medicationParams.getMrjc());
                pretimeDoseEt.setText(TextUtils.isEmpty(medicationParams.getMcjk()) ? "6" : medicationParams.getMcjk());
                
                // 恢复蜜丸单位数量
                if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm) && !TextUtils.isEmpty(medicationParams.getPillUnitCount())) {
                    try {
                        selectedUnitCount = Integer.parseInt(medicationParams.getPillUnitCount());
                    } catch (NumberFormatException e) {
                        selectedUnitCount = 1;
                    }
                }
                
                // 恢复水丸单位数量
                if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm) && !TextUtils.isEmpty(medicationParams.getWaterPillUnitCount())) {
                    try {
                        selectedWaterPillUnitCount = Integer.parseInt(medicationParams.getWaterPillUnitCount());
                    } catch (NumberFormatException e) {
                        selectedWaterPillUnitCount = 1;
                    }
                }
            }
        } else {
            if (hasPreDayDoseEt(mDrugForm)) {
                predayDoseEt.setText(getPreDayDoseEt(mDrugForm));
            } else {
                predayDoseEt.setText("3");
            }
            if (hasPreTimeDoseEt(mDrugForm)) {
                pretimeDoseEt.setText(getPreTimeDoseEt(mDrugForm));
            } else {
                pretimeDoseEt.setText("6");
            }
        }

        capsuleNumTv.setVisibility(View.GONE);
        
        // 根据剂型更新显示
        if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)) {
            // 蜜丸剂型：处理规格解析和显示更新
            if (mFormDetailMsg != null && mFormDetailMsg.getPackageSpecList() != null && !mFormDetailMsg.getPackageSpecList().isEmpty()) {
                if (!TextUtils.isEmpty(selectedSpecification)) {
                    parseSpecificationData(selectedSpecification);
                }
            }
            updatePillDosageDisplay();
        } else if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)) {
            // 水丸剂型：处理规格解析和显示更新
            if (mFormDetailMsg != null && mFormDetailMsg.getPackageSpecList() != null && !mFormDetailMsg.getPackageSpecList().isEmpty()) {
                if (!TextUtils.isEmpty(selectedSpecification)) {
                    parseWaterPillSpecificationData(selectedSpecification);
                }
            }
            updateWaterPillDosageDisplay();
        }
        
        // 如果没有规格数据，则使用原有的逻辑（针对蜜丸和水丸）
        boolean needsOriginalLogic = false;
        if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)) {
            needsOriginalLogic = TextUtils.isEmpty(currentSpecUnit) || currentSpecGramPerUnit <= 0 || specificationItems.isEmpty();
        } else if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)) {
            needsOriginalLogic = TextUtils.isEmpty(currentWaterPillSpecUnit) || currentWaterPillSpecGramPerUnit <= 0 || specificationItems.isEmpty();
        } else {
            needsOriginalLogic = true; // 其他剂型使用原有逻辑
        }
        
        if (needsOriginalLogic) {
            //设置pretimeDoseEt不可编辑
            pretimeDoseEt.setFocusable(false);
            pretimeDoseEt.setFocusableInTouchMode(false);

            pretimeDoseEt.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mBottomPopWindow.setPopTitle("选择用量");
                    mBottomPopWindow.setPopContentData(initUsageData());
                    mBottomPopWindow.showPop();
                    mBottomPopWindow.setBottomViewGone();
                    mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                        @Override
                        public void onPopItemClick(int position, PopItem popItem) {
                            try {
                            pretimeDoseEt.setText(popItem.getName().replace("g", ""));
                            mBottomPopWindow.dimissPop();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        }
                    });
                }
            });
        }
        //基础药费
//        if (isShowMedicalServiceFee) {
//            baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
//                    ? "¥ 0.00" : "¥ " + getMedicinePrice(true));
//        } else {
        baseMedicinePriceTv.setText(TextUtils.isEmpty(getMedicinePrice(true))
                ? "¥ 0.00" : "¥ " +/* DecimalUtils.div(*/getMedicinePrice(true)/*, PRICE_CONVERSION_RATIO)*/);
//        }
        //总药费
        // TODO: 2022/6/10 bug
//        medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(true));
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(true));
        //制作费
        makeMedicinePriceTv.setText(TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥ 0.00" : "¥ " + medicationParams.getMakeCost());
        //总计
//        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(true));
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(true));
    }

    /**
     * 显示颗粒、饮片、外用中药的用法用量信息
     *
     * @param isCache 是否显示的是缓存中的信息
     */
    private void showDoseMsgForKYExMedicine(boolean isCache) {
        showUsageAndDosageUi(false);
        if (isCache) {
            if (medicationParams != null) {
                totalDoseEt.setText(TextUtils.isEmpty(medicationParams.getTotalPreNum()) ? "7" : medicationParams.getTotalPreNum());
                dayDoseEt.setText(TextUtils.isEmpty(medicationParams.getDayPreNum()) ? "1" : medicationParams.getDayPreNum());
                preDoseTimeEt.setText(TextUtils.isEmpty(medicationParams.getPreTimes()) ? "2" : medicationParams.getPreTimes());
            }
        } else {
            if (TextUtils.isEmpty(totalDoseEt.getText())) {
                totalDoseEt.setText("7");
            } else {
                totalDoseEt.setText(totalDoseEt.getText());
            }
            if (TextUtils.isEmpty(dayDoseEt.getText())) {
                dayDoseEt.setText("1");
            } else {
                dayDoseEt.setText(dayDoseEt.getText());
            }
            if (TextUtils.isEmpty(preDoseTimeEt.getText())) {
                // 修改：判断是否为特定剂型，如果是则默认为3次，否则为2次
                if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)
                        || PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm)) {
                    preDoseTimeEt.setText("3");
                } else {
                    preDoseTimeEt.setText("2");
                }
            } else {
                preDoseTimeEt.setText(preDoseTimeEt.getText());
            }
        }

        // 当剂型为代煎时，设置preDoseTimeEt（每剂分xx次服用）的特殊处理
        if (PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) || TextUtils.equals("代煎", medicationParams.getDrugForm())) {
            // 检查当前值是否大于4，如果是则调整为4并提示
            if (!TextUtils.isEmpty(preDoseTimeEt.getText())) {
                try {
                    int currentValue = Integer.parseInt(preDoseTimeEt.getText().toString());
                    if (currentValue > 4) {
                        preDoseTimeEt.setText("2");
//                        ToastUtils.showShortMsg(mContext, "代煎剂型每剂最多分4次服用");
                    }
                } catch (NumberFormatException e) {
                    // 如果不是有效数字，设置为默认值2
                    preDoseTimeEt.setText("2");
                }
            }

            // 设置不可直接编辑
            preDoseTimeEt.setFocusable(false);
            preDoseTimeEt.setFocusableInTouchMode(false);

            // 添加点击事件，弹出底部选择框
            preDoseTimeEt.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //其他地方键盘消失
                    InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);

                    // 清除所有输入框的焦点
                    if (mLayoutRoot != null) {
                        // 方法1：将焦点转移到根布局
                        mLayoutRoot.requestFocus();

                        // 方法2：或者查找当前有焦点的视图并清除焦点
                        View currentFocus = getActivity().getCurrentFocus();
                        if (currentFocus != null) {
                            currentFocus.clearFocus();
                        }
                    }

                    // 初始化选项数据
                    List<PopItem> timesItems = new ArrayList<>();
                    String[] timesArray = {"1 次", "2 次", "3 次", "4 次"};
                    for (int i = 0; i < timesArray.length; i++) {
                        PopItem popItem = new PopItem();
                        popItem.setName(timesArray[i]);
                        popItem.setValue(String.valueOf(i + 1)); // 值为1-4
                        timesItems.add(popItem);
                    }

                    // 设置弹窗
                    if (mBottomPopWindow == null) {
                        mBottomPopWindow = new BottomPopWindow(mContext);
                    }
                    // 重置弹窗状态，确保没有残留的底部按钮或删除按钮
                    mBottomPopWindow.resetState();
                    mBottomPopWindow.setPopTitle("选择次数");
                    mBottomPopWindow.setPopContentData(timesItems);
                    mBottomPopWindow.showPop();

                    // 设置选项点击事件
                    mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                        @Override
                        public void onPopItemClick(int position, PopItem popItem) {
                            preDoseTimeEt.setText(popItem.getValue());
                            mBottomPopWindow.dimissPop();
                        }
                    });
                }
            });
        } else {
            // 非代煎剂型，恢复正常编辑状态
            preDoseTimeEt.setFocusable(true);
            preDoseTimeEt.setFocusableInTouchMode(true);
            preDoseTimeEt.setOnClickListener(null);
        }

        countGeneralMedicineTotalPrice(totalDoseEt.getText().toString().trim());
        //药费
//        medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(false));
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
        //总计
//        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(false));
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));

        //显示用药方法行


        capsuleNumTv.setVisibility(View.GONE);

    }

    /**
     * 显示饮片代煎信息
     *
     * @param isSpecial 是否是特殊剂型
     */
    private void showDoseMsgForYMedicine(boolean isSpecial) {
        boolean isDaijian = false;
        if (medicationParams != null) {
            isDaijian = TextUtils.equals("代煎", medicationParams.getDrugForm());
        }
        daiJianSwitch.setOnCheckedChangeListener(null);
        if (isDaijian) {
//            daiJianSwitch.setChecked(medicationParams.isUseDaiJian());
            daiJianSwitch.setChecked(true);

//            if (daiJianSwitch.isChecked()) {
                makeMedicinePriceLl.setVisibility(View.VISIBLE);
//            } else {
//                makeMedicinePriceLl.setVisibility(View.GONE);
//            }
            daiJianRl.setVisibility(View.VISIBLE);
            daiJianLine.setVisibility(View.VISIBLE);
            daiJianPianhaoRl.setVisibility(View.VISIBLE);
            daijianPianhaoLine.setVisibility(View.VISIBLE);
        } else {
            daiJianRl.setVisibility(View.GONE);
            daiJianLine.setVisibility(View.GONE);
            daiJianSwitch.setChecked(false);
            daiJianPianhaoRl.setVisibility(View.GONE);
            daijianPianhaoLine.setVisibility(View.GONE);
        }

        //代煎开关
        daiJianSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                medicationParams.setUseDaiJian(isChecked);
                if (isChecked) {
                    makeMedicinePriceLl.setVisibility(View.VISIBLE);
                } else {
                    makeMedicinePriceLl.setVisibility(View.GONE);
                }
                String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                        ? "7" : totalDoseEt.getText().toString();//付数
                String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                        ? "¥0.00" : "¥" + medicationParams.getMakeCost();//制作费
                //制作费,代煎费
                makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" + (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));

                //药费
//                medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(false));
                medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
                //总计
//                olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(false));
                olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
            }
        });
        //药费
        // TODO: 2022/6/10 此处有bug，药费应是 getTotalMedicineFee
//        medicineTotalPriceTv.setText("¥ " + countMedicineTotalPrice(false));
        medicineTotalPriceTv.setText("¥ " + getTotalMedicineFee(false));
        String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                ? "7" : totalDoseEt.getText().toString();//付数
        String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                ? "¥0.00" : "¥" + medicationParams.getMakeCost();//制作费
        //制作费,代煎费
        makeMedicinePriceTv.setText(makeFeePre + " * " + num + " = ¥" + (new BigDecimal(getMedicineMakeFee(false)).setScale(2).toString()));
        //总计
//        olderTotalpriceTv.setText("¥ " + countPrescriptionTotalPrice(false));
        olderTotalpriceTv.setText("¥ " + getTotalPrescriptionPrice(false));
    }

//    /**
//     * 计算订单总金额 ：总药费+诊费*1.064（+增值税+手续费）
//     * 增值税计算公式：（AX/(1-A)-X/0.7*0.3)*0.06*1.12
//     * 手续费计算公式:（AX/(1-A)-X/0.7*0.3)*0.01
//     * A：医技服务费比例 <30% 时订单总金额不加增值税 和手续费
//     * X：基础药费
//     * medicalFee 诊费
//     *
//     * @param isSpecialDosageForm 是否为特殊剂型
//     * @return 订单总金额
//     */
//    private String countPrescriptionTotalPrice(boolean isSpecialDosageForm) {
//        String totalPrice = "0";
//        //诊费
//        String fee = TextUtils.isEmpty(additionalChargeEt.getText().toString())
//                ? "0" : additionalChargeEt.getText().toString();
//        //医技服务费
//        String doctorServiceFee = countDoctorServiceFeeOriginal(isSpecialDosageForm);
//        //总药费
//        String totalMedicinePrice = countMedicineTotalPriceOrignal(isSpecialDosageForm, doctorServiceFee);
//        //诊费*1.064
//        String medicineFees = "0";
//        //增值税和手续费
//        String addedValueTax = "0";
//        //X/0.7*0.3的值,就是老版药价比新版药价多的值
//        String moreMedicinePrice = "0";
//        //（AX/(1-A)-X/0.7*0.3)的计算值，即医技服务费-老版药价比新版药价多的值
//        String value = "0";
//        double f_newRadio = 0.0;
//        String radioStr = newDoctorServiceRadio;
//        if (!TextUtils.isEmpty(radioStr)) {
//            f_newRadio = Double.parseDouble(radioStr);
//        }
//        if (!TextUtils.isEmpty(fee)) {
//            medicineFees = DecimalUtils.multiply(fee, "1.064");
//        }
//        if (f_newRadio < 0.30) {
//            totalPrice = DecimalUtils.add(totalMedicinePrice, medicineFees);
//        } else {
//            moreMedicinePrice = DecimalUtils.division(getMedicinePrice(isSpecialDosageForm), "0.7");
//            moreMedicinePrice = DecimalUtils.multiply(moreMedicinePrice, "0.3");
//            value = DecimalUtils.subtraction(doctorServiceFee, moreMedicinePrice);
//            addedValueTax = DecimalUtils.multiply(value, "0.0772");
//            totalPrice = DecimalUtils.add(totalMedicinePrice, medicineFees, addedValueTax);
//        }
//        return totalPrice;
//    }

    /**
     * @return 计算订单总金额(默认的) ：
     * 诊费+服务费（诊费*6%）=总金额
     */
    private String countDefaultPrescriptionTotalPrice() {
        String totalPrice = "0";
        String serviceFee = "0";//服务费
        String fee = TextUtils.isEmpty(additionalChargeEt.getText().toString())
                ? "0" : additionalChargeEt.getText().toString();//诊费
        if (!TextUtils.isEmpty(fee)) {
            serviceFee = DecimalUtils.mul(fee, "0.064");
        }
        totalPrice = DecimalUtils.add(fee, serviceFee);
        return totalPrice;
    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     *                            公式：A*X/（1-A）
     *                            A ：医技服务费比例，通过接口获取，默认30%
     *                            X ：新药价，计算所得（getMedicinePrice（））
     * @return 计算医技服务费 保留两位小数，用于现在在界面
     */
    private String countDoctorServiceFee(boolean isSpecialDosageForm) {
        String serviceFee = "0";//服务费
        serviceFee = countDoctorServiceFeeOriginal(isSpecialDosageForm);
        return DecimalUtils.format(serviceFee);
    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     *                            公式：A*X/（1-A）
     *                            A ：医技服务费比例，通过接口获取，默认30%
     *                            X ：新药价，计算所得（getMedicinePrice（））
     * @return 计算医技服务费 结果不做处理
     */
    private String countDoctorServiceFeeOriginal(boolean isSpecialDosageForm) {
        String serviceFee = "0";//服务费
        String remRadio = "";
        String fee = "";
        if (!TextUtils.isEmpty(newDoctorServiceRadio)) {
            double radio = Double.parseDouble(newDoctorServiceRadio);
            if (radio < 1) {
                remRadio = DecimalUtils.subtraction("1", newDoctorServiceRadio);
            } else {
                return "";
            }
        }
        fee = DecimalUtils.multiply(newDoctorServiceRadio, getMedicinePrice(isSpecialDosageForm));
        if (!TextUtils.isEmpty(remRadio)) {
            serviceFee = DecimalUtils.division(fee, remRadio);
        } else {
            serviceFee = "0.00";
        }
        return serviceFee;
    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     *                            公式：[AX/(1-A）]/[X+AX/(1-A)] 即：医技服务费/（基础药费+医技服务费）
     *                            AX/(1-A):医技服务费  医技服务费输入框中的内容
     *                            X ：基础药价  getMedicinePrice（）方法计算所得
     * @return 医技服务费新比例  计算结果向下取整，保留两位小数
     */
    private String countNewDoctorServiceRadio(boolean isSpecialDosageForm) {
        return countNewDoctorServiceRadioOriginal(isSpecialDosageForm);
    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     *                            公式：[AX/(1-A）]/[X+AX/(1-A)] 即：医技服务费/（基础药费+医技服务费）
     *                            AX/(1-A):医技服务费  医技服务费输入框中的内容
     *                            X ：基础药价  getMedicinePrice（）方法计算所得
     * @return 医技服务费新比例  计算结果保留原始
     */
    private String countNewDoctorServiceRadioOriginal(boolean isSpecialDosageForm) {
        LogUtils.e(MedicationFragment.class, "text=" + doctorServiceFeeEt.getText().toString());
        String newRadio = "";
        String doctorServiceFee = TextUtils.isEmpty(doctorServiceFeeEt.getText().toString()) ? "0" : doctorServiceFeeEt.getText().toString();
        String baseMedicineprice = getMedicinePrice(isSpecialDosageForm);
        //基础药费和医技服务费相加的原始结果，小数位不处理
        String medicineAddServiceFee = DecimalUtils.adding(doctorServiceFee, baseMedicineprice);
        newRadio = DecimalUtils.div(doctorServiceFee, medicineAddServiceFee, 8, BigDecimal.ROUND_DOWN);
        return newRadio;
    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     *                            公式：X+AX/(1-A)+Z
     *                            X 药价
     *                            AX/(1-A) 医技服务费,可以手动输入
     *                            Z 制作费（特殊剂型有）
     * @return 计算药费总价
     */
    private String countMedicineTotalPrice(boolean isSpecialDosageForm) {
        String totalMedicinePrice = "0";
        String serviceFee = "0";
        String medicinePrice = getMedicinePrice(isSpecialDosageForm);
//        serviceFee = TextUtils.isEmpty(doctorServiceFeeEt.getText().toString())
//                ? "0" : doctorServiceFeeEt.getText().toString();
        serviceFee = countDoctorServiceFeeOriginal(isSpecialDosageForm);
        String makeFee = getMedicineMakeFee(isSpecialDosageForm);
        totalMedicinePrice = DecimalUtils.add(medicinePrice, serviceFee, makeFee);
        return totalMedicinePrice;
    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     *                            公式：X+AX/(1-A)+Z
     *                            X 药价
     *                            AX/(1-A) 医技服务费,可以手动输入
     *                            Z 制作费（特殊剂型有）
     * @return 计算药费总价 计算结果不做处理，保留原有小数,用于计算订单总金额
     */
    private String countMedicineTotalPriceOrignal(boolean isSpecialDosageForm, String serviceFee) {
        String totalMedicinePrice = "0";
        String medicinePrice = getMedicinePrice(isSpecialDosageForm);
        String makeFee = getMedicineMakeFee(isSpecialDosageForm);
        totalMedicinePrice = DecimalUtils.adding(medicinePrice, serviceFee, makeFee);
        return totalMedicinePrice;
    }

    /**
     * @param isSpecialDosageForm
     * @return 总药费==总基础药费+制作费（特殊剂型制作费/饮片的代煎费用）
     */
    private String getTotalMedicineFee(boolean isSpecialDosageForm) {
        return DecimalUtils.add(getMedicinePrice(isSpecialDosageForm), getMedicineMakeFee(isSpecialDosageForm));

    }

    /**
     * @return 总基础药费+诊费*1.064+制作费（特殊剂型制作费/饮片的代煎费用）
     */
    private String getTotalPrescriptionPrice(boolean isSpecialDosageForm) {
        //总药费+诊费*1.064
        return DecimalUtils.add(getTotalMedicineFee(isSpecialDosageForm), getServiceFeeNew());
    }

    /**
     * @return 诊费*1.064
     */
    private String getServiceFeeNew() {
        String fee = "0";
        if (additionalChargeEt != null) {
            fee = additionalChargeEt.getText().toString().trim();
        }
        if (TextUtils.isEmpty(fee)) {
            return "0";
        }
        BigDecimal feeDec = new BigDecimal(fee);
        if (feeDec.floatValue() < 0.01) {
            return "0";
        }
        return feeDec.multiply(new BigDecimal(1.064)).toString();
    }

    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     * @return 获取药材制作费  普通剂型为0
     */
    /**
     * @param isSpecialDosageForm 是否为特殊剂型
     * @return 获取药材制作费  当剂型为代煎、颗粒、外用中药且设置了制作费时，制作费 = 单剂制作费 × 付数；其他情况为0
     */
    private String getMedicineMakeFee(boolean isSpecialDosageForm) {
        String makeFee = "0";
        if (isSpecialDosageForm) {
            // 特殊剂型的制作费处理保持不变
            if (medicationParams != null) {
                makeFee = TextUtils.isEmpty(medicationParams.getMakeCost())
                        ? "0" : medicationParams.getMakeCost(); //制作费
            }
        } else {
            // 新增: 处理代煎、颗粒、外用中药的制作费计算
            if (medicationParams != null &&
                    (TextUtils.equals("代煎", medicationParams.getDrugForm()) ||
                            TextUtils.equals("颗粒", medicationParams.getDrugForm()) ||
                            TextUtils.equals("外用中药", medicationParams.getDrugForm()))) {

                // 检查是否设置了制作费
                String makeFeePre = TextUtils.isEmpty(medicationParams.getMakeCost())
                        ? "0" : medicationParams.getMakeCost(); //单剂制作费

                // 只有在有制作费的情况下才进行计算
                if (!"0".equals(makeFeePre)) {
                    String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                            ? "7" : totalDoseEt.getText().toString(); //付数
                    // 总制作费 = 单剂制作费 × 付数
                    makeFee = new BigDecimal(makeFeePre)
                            .multiply(new BigDecimal(num))
                            .setScale(2)
                            .toString();
                }
            }
        }
        return makeFee;
    }

    /**
     * @param isSpecialDosageForm 是否是特殊剂型
     * @return 基础药费
     */
    private String getMedicinePrice(boolean isSpecialDosageForm) {
        String drugPrice = "0";//药费
        String num = TextUtils.isEmpty(totalDoseEt.getText().toString())
                ? "7" : totalDoseEt.getText().toString();//付数
        if (isSpecialDosageForm) {
            if (medicationParams != null) {
                drugPrice = TextUtils.isEmpty(medicationParams.getDrugPrice())
                        ? "0" : medicationParams.getDrugPrice();
            }
        } else {
            drugPrice = DecimalUtils.format(countMedicinePrice(num), 3);
        }
        return drugPrice;
    }

    /**
     * 计算预计服用天数
     */
    private String countTakeDays() {
        String takeDays = "1";
        int takeDayInt = 1;
        //总重
        String totalWeight = "";
        if (medicationParams != null) {
            totalWeight = TextUtils.isEmpty(medicationParams.getBalanceWeight())
                    ? "0" : medicationParams.getBalanceWeight();
        }
        //每天多少次
        String predayDose = TextUtils.isEmpty(predayDoseEt.getText().toString().trim())
                ? "0" : predayDoseEt.getText().toString().trim();
        // 每次多少g
        String pretimeDose = TextUtils.isEmpty(pretimeDoseEt.getText().toString().trim())
                ? "0" : pretimeDoseEt.getText().toString().trim();
        //一天吃多少g
        String dayDose = DecimalUtils.mul(predayDose, pretimeDose);
        if (TextUtils.isEmpty(dayDose) || "0.00".equals(dayDose) || TextUtils.isEmpty(totalWeight)
                || "0".equals(totalWeight)) {
            takeDays = "1";
        } else {
            takeDays = DecimalUtils.div(totalWeight, dayDose, 0, BigDecimal.ROUND_DOWN);
        }
        takeDayInt = Integer.parseInt(takeDays);
        if (takeDayInt < 1) {
            takeDays = "1";
        }
        return takeDays;

    }

    /**
     * 初始化用量数据
     */
    private List<PopItem> initUsageData() {
        List<PopItem> usageData = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            PopItem item = new PopItem();
            item.setName(i * 3 + "g");
            usageData.add(item);
        }
        return usageData;
    }


    /**
     * 显示用法用量ui
     *
     * @param isSpecial false 显示颗粒、饮片、外用中药的 true 显示蜜丸水丸散剂膏方等的用法用量
     */
    private void showUsageAndDosageUi(boolean isSpecial) {
        if (isSpecial) {
            normalUsageAndDosageLl.setVisibility(View.GONE);
            specialUsageAndDosageLl.setVisibility(View.VISIBLE);
            makeMedicinePriceLl.setVisibility(View.VISIBLE);
            
            // 控制规格行的显示：蜜丸、水丸、胶囊、膏方、散剂显示规格行，但前提是有规格数据
            if ((PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm) ||
                PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)) 
                && !specificationItems.isEmpty()) {  // 检查是否有规格数据
                totalDoseLine.setVisibility(View.VISIBLE);
                specificationLl.setVisibility(View.VISIBLE);
                specificationLine.setVisibility(View.VISIBLE);
            } else {
                totalDoseLine.setVisibility(View.GONE);
                specificationLl.setVisibility(View.GONE);
                specificationLine.setVisibility(View.GONE);
            }
        } else {
            normalUsageAndDosageLl.setVisibility(View.VISIBLE);
            specialUsageAndDosageLl.setVisibility(View.GONE);
            makeMedicinePriceLl.setVisibility(View.GONE);
            // 普通剂型不显示规格行
            specificationLl.setVisibility(View.GONE);
            specificationLine.setVisibility(View.GONE);
        }
        showDoseMsgForYMedicine(isSpecial);
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == REQUESTCODE && resultCode == RESULT_OK) {
            if (data != null) {
                PopItem popItem = new PopItem();
                popItem.setName(data.getStringExtra("name"));
                popItem.setAge(data.getStringExtra("age"));
                popItem.setSex(data.getStringExtra("sex"));
                popItem.setIsPregnant(data.getStringExtra("isPregnant"));
                popItem.setId(data.getStringExtra("patientId"));
                popItem.setBirthday(data.getStringExtra("birthday"));
// (每次点击更换都会重新请求，就不用本地添加了)
// patientPopItems.add(1, popItem);//新增的患者倒序显示（患者本人一直显示第一个）
                checkIsClearData(popItem);

            }
        } else if (requestCode == REQUESTCODE_EDIT && resultCode == RESULT_OK) {
            String takerId = data.getStringExtra("patientId");
            if (patientPopItems != null && patientPopItems.size() > 0) {
                for (int i = 0; i < patientPopItems.size(); i++) {
                    if (takerId.equals(patientPopItems.get(i).getId())) {
                        patientPopItems.get(i).setName(data.getStringExtra("name"));
                        patientPopItems.get(i).setAge(data.getStringExtra("age"));
                        patientPopItems.get(i).setSex(data.getStringExtra("sex"));
                        patientPopItems.get(i).setIsPregnant(data.getStringExtra("isPregnant"));
                        patientPopItems.get(i).setBirthday(data.getStringExtra("birthday"));
                        checkIsClearData(patientPopItems.get(i));
                        updateCacheMsg(takerId, patientPopItems.get(i));
                        PopItem item = patientPopItems.get(i);
                        if (patientPopItems.get(i).isMark()) {
                            Contacts contacts = contactsDao.load(takerId + "_" + userId);
                            contacts.setNickName(item.getName());
                            contacts.setSex(item.getSex());
                            contacts.setAge(item.getAge());
                            contactsDao.update(contacts);
                            Session session = sessionDao.load(takerId + "_" + userId);
                            if (session != null) {
                                session.setName(item.getName());
                                session.setAge(item.getAge());
                                session.setSex(item.getSex());
                                sessionDao.update(session);
                            }
                            EventBusUtils.post(new PatientInfoChangeEvent(takerId, PatientInfoChangeEvent.CHANGE_INFO));
                        }
                        break;
                    }
                }

            }

        }

    }

    private void updateCacheMsg(String takerId, PopItem popItem) {
        DataCache cachePresMsg = DataCacheDaoUtil.getInstance()
                .select(DataCacheType.CACHE_PRESCRIPTION_MSG, userId + "_" + patientId);

        if (cachePresMsg != null) {
            try {
                OrderMsgBean cacheOrderMsgBean = JSON.parseObject(cachePresMsg.getValue(), OrderMsgBean.class);
                if (cacheOrderMsgBean != null && !TextUtils.isEmpty(takerId) && takerId.equalsIgnoreCase(cacheOrderMsgBean.getTakerId())) {
                    cacheOrderMsgBean.setTakerName(popItem.getName());
                    cacheOrderMsgBean.setTakerSex(popItem.getSex());
                    cacheOrderMsgBean.setTakerAge(popItem.getAge());
                    cacheOrderMsgBean.setTakerIsPregnant(popItem.getIsPregnant());
                    cachePresMsg.setValue(JSON.toJSONString(cacheOrderMsgBean));
                    DataCacheDaoUtil.getInstance().upDateData(cachePresMsg);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 设置要缓存的药方信息
     *
     * @return
     */
    public DataCache setCachePresMsg() {
        DataCache data = new DataCache();
        data.setKey(userId + "_" + patientId);
        data.setType(DataCacheType.CACHE_PRESCRIPTION_MSG);
        data.setValue(JSON.toJSONString(setMedicationParams()));
        System.out.println("普通开方设置要保存的药方数据：" + patientId + "===============" + JSON.toJSONString(setMedicationParams()));
        return data;
    }


    @Override
    public void onDestroy() {
        EventBusUtils.unRegister(this);
        if (mBottomPopWindow != null) {
            mBottomPopWindow.dimissPop();
            mBottomPopWindow = null;
        }
        if (mDosageFormsPop != null) {
            mDosageFormsPop.dismiss();
            mDosageFormsPop = null;
        }
        cancelRequest(getMedicalFeeCallBack, getDosageFormListCallBack, getTabooTipsCallBack
                , addCustomTakingTimeCallBack, getPatientsCallBack, getTakingTimeCallBack
                , getMedicalServiceFeeRadioCallBack, saveCommonTemplateCallBack);
        super.onDestroy();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // TODO: inflate a fragment view
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        unbinder = ButterKnife.bind(this, rootView);
        return rootView;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        unbinder.unbind();
    }

    /**
     * 初始化规格选择数据
     */
    private void initSpecificationData() {
        specificationItems.clear();
        boolean hasCheckedSpec = false;

        // 使用接口返回的 packageSpecList 数据
        if (mFormDetailMsg != null && mFormDetailMsg.getPackageSpecList() != null
                && !mFormDetailMsg.getPackageSpecList().isEmpty()) {
            for (DosageFormBean.FormList.FormDetailMsg.PackageSpec packageSpec : mFormDetailMsg.getPackageSpecList()) {
                if (packageSpec != null && !TextUtils.isEmpty(packageSpec.getValue())) {
                    PopItem item = new PopItem();
                    item.setName(packageSpec.getValue());  // 直接显示 value 字段的数据
                    item.setValue(packageSpec.getValue());
                    specificationItems.add(item);

                    // 如果有默认选中的规格，提前记录
                    if (!hasCheckedSpec && packageSpec.isChecked()) {
                        hasCheckedSpec = true;
                        selectedSpecification = packageSpec.getValue();
                        if (specificationTv != null) {
                            specificationTv.setText(packageSpec.getValue());
                            specificationTv.setTextColor(getResources().getColor(R.color.br_color_black_666));
                        }
                    }
                }
            }
        }


        // 如果没有选中的规格，使用第一个作为默认选中项
        if (!hasCheckedSpec && !specificationItems.isEmpty()) {
            selectedSpecification = specificationItems.get(0).getValue();
            if (specificationTv != null) {
                specificationTv.setText(specificationItems.get(0).getName());
                specificationTv.setTextColor(getResources().getColor(R.color.br_color_black_666));
            }
        } else if (!hasCheckedSpec) {
            selectedSpecification = "";
            if (specificationTv != null) {
                specificationTv.setText("请选择规格");
                specificationTv.setTextColor(getResources().getColor(R.color.br_color_black_999));
            }
        }
        
        // 解析或重置蜜丸相关的规格数据
        if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)) {
            // 蜜丸剂型：解析当前选中的规格数据
            if (!TextUtils.isEmpty(selectedSpecification)) {
                parseSpecificationData(selectedSpecification);
            }
        } else {
            // 非蜜丸剂型：重置蜜丸特有的状态变量，避免状态泄露
            resetPillSpecificState();
        }
        
        // 强制刷新UI以确保显示状态与当前剂型一致
        updatePillDosageDisplay();
        
        // 当没有接口数据时，不添加任何数据，规格行将被隐藏
    }

    /**
     * 重置蜜丸特有的状态变量，避免状态泄露
     */
    private void resetPillSpecificState() {
        currentSpecUnit = "";
        currentSpecGramPerUnit = 0.0;
        selectedUnitCount = 1; // 重置为默认值
    }
    
    /**
     * 解析规格数据，提取单位和克数
     * @param specValue 规格值，如"3g/丸"
     */
    private void parseSpecificationData(String specValue) {
        if (TextUtils.isEmpty(specValue)) {
            currentSpecUnit = "";
            currentSpecGramPerUnit = 0.0;
            return;
        }
        
        // 使用正则表达式解析规格，匹配如"3g/丸"、"0.5g/粒"等格式
        Pattern pattern = Pattern.compile("([0-9.]+)g/(.+)");
        Matcher matcher = pattern.matcher(specValue);
        
        if (matcher.find()) {
            try {
                currentSpecGramPerUnit = Double.parseDouble(matcher.group(1));
                currentSpecUnit = matcher.group(2);
            } catch (NumberFormatException e) {
                currentSpecGramPerUnit = 0.0;
                currentSpecUnit = "";
            }
        } else {
            currentSpecGramPerUnit = 0.0;
            currentSpecUnit = "";
        }
    }
    
    /**
     * 解析胶囊规格数据，提取单位和克数
     * @param specValue 规格值，如"0.5g/粒"
     */
    private void parseCapsuleSpecificationData(String specValue) {
        if (TextUtils.isEmpty(specValue)) {
            currentCapsuleSpecUnit = "";
            currentCapsuleSpecGramPerUnit = 0.0;
            return;
        }
        
        // 使用正则表达式解析规格，匹配如"0.5g/粒"、"1g/粒"等格式
        Pattern pattern = Pattern.compile("([0-9.]+)g/(.+)");
        Matcher matcher = pattern.matcher(specValue);
        
        if (matcher.find()) {
            try {
                currentCapsuleSpecGramPerUnit = Double.parseDouble(matcher.group(1));
                currentCapsuleSpecUnit = matcher.group(2);
            } catch (NumberFormatException e) {
                currentCapsuleSpecGramPerUnit = 0.0;
                currentCapsuleSpecUnit = "";
            }
        } else {
            currentCapsuleSpecGramPerUnit = 0.0;
            currentCapsuleSpecUnit = "";
        }
    }
    
    /**
     * 解析水丸规格数据，提取单位和克数
     * @param specValue 规格值，如"3g/丸"
     */
    private void parseWaterPillSpecificationData(String specValue) {
        if (TextUtils.isEmpty(specValue)) {
            currentWaterPillSpecUnit = "";
            currentWaterPillSpecGramPerUnit = 0.0;
            return;
        }
        
        // 使用正则表达式解析规格，匹配如"3g/丸"、"0.5g/粒"等格式
        Pattern pattern = Pattern.compile("([0-9.]+)g/(.+)");
        Matcher matcher = pattern.matcher(specValue);
        
        if (matcher.find()) {
            try {
                currentWaterPillSpecGramPerUnit = Double.parseDouble(matcher.group(1));
                currentWaterPillSpecUnit = matcher.group(2);
            } catch (NumberFormatException e) {
                currentWaterPillSpecGramPerUnit = 0.0;
                currentWaterPillSpecUnit = "";
            }
        } else {
            currentWaterPillSpecGramPerUnit = 0.0;
            currentWaterPillSpecUnit = "";
        }
    }
    
    /**
     * 解析膏方规格数据，提取单位和克数
     * @param specValue 规格值，如"10g/勺"
     */
    private void parseCreamFormulaSpecificationData(String specValue) {
        if (TextUtils.isEmpty(specValue)) {
            currentCreamFormulaSpecUnit = "";
            currentCreamFormulaSpecGramPerUnit = 0.0;
            return;
        }
        
        // 使用正则表达式解析规格，匹配如"10g/勺"、"20g/瓶"等格式
        Pattern pattern = Pattern.compile("([0-9.]+)g/(.+)");
        Matcher matcher = pattern.matcher(specValue);
        
        if (matcher.find()) {
            try {
                currentCreamFormulaSpecGramPerUnit = Double.parseDouble(matcher.group(1));
                currentCreamFormulaSpecUnit = matcher.group(2);
            } catch (NumberFormatException e) {
                currentCreamFormulaSpecGramPerUnit = 0.0;
                currentCreamFormulaSpecUnit = "";
            }
        } else {
            currentCreamFormulaSpecGramPerUnit = 0.0;
            currentCreamFormulaSpecUnit = "";
        }
    }
    
    /**
     * 设置规格选择点击事件
     */
    private void setSpecificationClickListener() {
        // 设置整个规格行的点击事件
        specificationLl.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showSpecificationPop();
            }
        });
    }

    /**
     * 更新蜜丸剂型的用量显示
     */
    private void updatePillDosageDisplay() {
        if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm) && !TextUtils.isEmpty(currentSpecUnit) 
                && currentSpecGramPerUnit > 0 && !specificationItems.isEmpty()) {
            // 有规格数据时，显示"每次x丸(xg)"格式
            pretimeDoseUnitTv.setText(currentSpecUnit);
            
            // 显示单位数量而不是克数
            pretimeDoseEt.setText(String.valueOf(selectedUnitCount));
            
            // 更新每次用量的克数显示
            updatePillGramDisplay();
            
            // 将输入框设置为点击弹窗选择模式
            pretimeDoseEt.setFocusable(false);
            pretimeDoseEt.setFocusableInTouchMode(false);
            pretimeDoseEt.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showPillUnitCountPop();
                }
            });
        } else {
            // 非蜜丸剂型或无规格数据时，恢复标准显示
            pretimeDoseUnitTv.setText("g");
            pretimeDoseGramTv.setVisibility(View.GONE);
            pretimeDoseEt.setFocusable(true);
            pretimeDoseEt.setFocusableInTouchMode(true);
            pretimeDoseEt.setOnClickListener(null);
            
            // 如果不是蜜丸剂型，确保输入框不显示蜜丸的数量值
            if (!PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)) {
                // 对于非蜜丸剂型，不清空输入框内容，保持用户输入的值或默认值
                // pretimeDoseEt的内容将由各剂型的显示方法(如showDoseMsgForCreamFormula)来设置
            }
        }
    }
    
    /**
     * 更新蜜丸用量的克数显示
     */
    private void updatePillGramDisplay() {
        if (selectedUnitCount > 0 && currentSpecGramPerUnit > 0) {
            double totalGram = selectedUnitCount * currentSpecGramPerUnit;
            String gramText = String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
            pretimeDoseGramTv.setText(gramText);
            pretimeDoseGramTv.setVisibility(View.VISIBLE);
        }
    }
    
    /**
     * 更新胶囊剂型的用量显示
     */
    private void updateCapsuleDosageDisplay() {
        if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm) && !TextUtils.isEmpty(currentCapsuleSpecUnit) 
                && currentCapsuleSpecGramPerUnit > 0 && !specificationItems.isEmpty()) {
            // 有规格数据时，显示"每次x粒(xg)"格式
            pretimeDoseUnitTv.setText(currentCapsuleSpecUnit);
            
            // 显示单位数量而不是克数
            pretimeDoseEt.setText(String.valueOf(selectedCapsuleUnitCount));
            
            // 更新每次用量的克数显示
            updateCapsuleGramDisplay();
            
            // 将输入框设置为点击弹窗选择模式
            pretimeDoseEt.setFocusable(false);
            pretimeDoseEt.setFocusableInTouchMode(false);
            pretimeDoseEt.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showCapsuleUnitCountPop();
                }
            });
        } else {
            // 非胶囊剂型或无规格数据时，恢复标准显示
            pretimeDoseUnitTv.setText("g");
            pretimeDoseGramTv.setVisibility(View.GONE);
            pretimeDoseEt.setFocusable(true);
            pretimeDoseEt.setFocusableInTouchMode(true);
            pretimeDoseEt.setOnClickListener(null);
        }
    }
    
    /**
     * 更新胶囊用量的克数显示
     */
    private void updateCapsuleGramDisplay() {
        if (selectedCapsuleUnitCount > 0 && currentCapsuleSpecGramPerUnit > 0) {
            double totalGram = selectedCapsuleUnitCount * currentCapsuleSpecGramPerUnit;
            String gramText = String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
            pretimeDoseGramTv.setText(gramText);
            pretimeDoseGramTv.setVisibility(View.VISIBLE);
        }
    }
    
    /**
     * 更新水丸剂型的用量显示
     */
    private void updateWaterPillDosageDisplay() {
        if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm) && !TextUtils.isEmpty(currentWaterPillSpecUnit) 
                && currentWaterPillSpecGramPerUnit > 0 && !specificationItems.isEmpty()) {
            // 有规格数据时，显示"每次x丸(xg)"格式
            pretimeDoseUnitTv.setText(currentWaterPillSpecUnit);
            
            // 显示单位数量而不是克数
            pretimeDoseEt.setText(String.valueOf(selectedWaterPillUnitCount));
            
            // 更新每次用量的克数显示
            updateWaterPillGramDisplay();
            
            // 将输入框设置为点击弹窗选择模式
            pretimeDoseEt.setFocusable(false);
            pretimeDoseEt.setFocusableInTouchMode(false);
            pretimeDoseEt.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showWaterPillUnitCountPop();
                }
            });
        } else {
            // 非水丸剂型或无规格数据时，恢复标准显示
            pretimeDoseUnitTv.setText("g");
            pretimeDoseGramTv.setVisibility(View.GONE);
            pretimeDoseEt.setFocusable(true);
            pretimeDoseEt.setFocusableInTouchMode(true);
            pretimeDoseEt.setOnClickListener(null);
        }
    }
    
    /**
     * 更新水丸用量的克数显示
     */
    private void updateWaterPillGramDisplay() {
        if (selectedWaterPillUnitCount > 0 && currentWaterPillSpecGramPerUnit > 0) {
            double totalGram = selectedWaterPillUnitCount * currentWaterPillSpecGramPerUnit;
            String gramText = String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
            pretimeDoseGramTv.setText(gramText);
            pretimeDoseGramTv.setVisibility(View.VISIBLE);
        }
    }
    
    /**
     * 更新膏方剂型的用量显示
     */
    private void updateCreamFormulaDosageDisplay() {
        if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm) && !TextUtils.isEmpty(currentCreamFormulaSpecUnit) 
                && currentCreamFormulaSpecGramPerUnit > 0 && !specificationItems.isEmpty()) {
            
            // 特殊逻辑：当规格单位是"瓶"时，保持原有的每次xg输入方式
            if ("瓶".equals(currentCreamFormulaSpecUnit)) {
                // 保持原有逻辑，显示"每次xg"
                pretimeDoseUnitTv.setText("g");
                pretimeDoseGramTv.setVisibility(View.GONE);
                pretimeDoseEt.setFocusable(true);
                pretimeDoseEt.setFocusableInTouchMode(true);
                pretimeDoseEt.setOnClickListener(null);
                
                // 如果输入框为空或者切换了规格，设置默认值30g
                if (TextUtils.isEmpty(pretimeDoseEt.getText().toString().trim())) {
                    pretimeDoseEt.setText("30");
                }
            } else {
                // 有规格数据且单位不是"瓶"时，显示"每次x单位(xg)"格式
                pretimeDoseUnitTv.setText(currentCreamFormulaSpecUnit);
                
                // 显示单位数量而不是克数
                pretimeDoseEt.setText(String.valueOf(selectedCreamFormulaUnitCount));
                
                // 更新每次用量的克数显示
                updateCreamFormulaGramDisplay();
                
                // 将输入框设置为点击弹窗选择模式
                pretimeDoseEt.setFocusable(false);
                pretimeDoseEt.setFocusableInTouchMode(false);
                pretimeDoseEt.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        showCreamFormulaUnitCountPop();
                    }
                });
            }
        } else {
            // 非膏方剂型或无规格数据时，恢复标准显示
            pretimeDoseUnitTv.setText("g");
            pretimeDoseGramTv.setVisibility(View.GONE);
            pretimeDoseEt.setFocusable(true);
            pretimeDoseEt.setFocusableInTouchMode(true);
            pretimeDoseEt.setOnClickListener(null);
        }
    }
    
    /**
     * 更新膏方用量的克数显示
     */
    private void updateCreamFormulaGramDisplay() {
        if (selectedCreamFormulaUnitCount > 0 && currentCreamFormulaSpecGramPerUnit > 0) {
            double totalGram = selectedCreamFormulaUnitCount * currentCreamFormulaSpecGramPerUnit;
            String gramText = String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
            pretimeDoseGramTv.setText(gramText);
            pretimeDoseGramTv.setVisibility(View.VISIBLE);
        }
    }
    
    /**
     * 显示规格选择弹窗
     */
    private void showSpecificationPop() {
        if (specificationItems.size() > 0) {
            // 重置弹窗状态
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(specificationItems);
            mBottomPopWindow.setPopTitle("选择规格");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewGone();
            
            // 设置选项点击事件
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    selectedSpecification = popItem.getValue();
                    specificationTv.setText(popItem.getName());
                    specificationTv.setTextColor(getResources().getColor(R.color.br_color_black_666));
                    mBottomPopWindow.dimissPop();
                    
                    // 根据剂型解析新选择的规格数据并更新显示
                    if (PublicParams.DOSAGEFORM_HONEYED_PILL.equals(mDrugForm)) {
                        parseSpecificationData(selectedSpecification);
                        updatePillDosageDisplay();
                    } else if (PublicParams.DOSAGEFORM_CAPSULE_PILL.equals(mDrugForm)) {
                        parseCapsuleSpecificationData(selectedSpecification);
                        updateCapsuleDosageDisplay();
                    } else if (PublicParams.DOSAGEFORM_WATERED_PILL.equals(mDrugForm)) {
                        parseWaterPillSpecificationData(selectedSpecification);
                        updateWaterPillDosageDisplay();
                    } else if (PublicParams.DOSAGEFORM_CREAM_FORMULA.equals(mDrugForm)) {
                        // 保存之前的单位类型用于判断是否需要重置
                        String previousUnit = currentCreamFormulaSpecUnit;
                        
                        parseCreamFormulaSpecificationData(selectedSpecification);
                        
                        // 检查单位是否发生变化，如果从"瓶"切换到其他单位，或从其他单位切换到"瓶"，则重置数量
                        boolean needReset = false;
                        if (!TextUtils.isEmpty(previousUnit) && !TextUtils.isEmpty(currentCreamFormulaSpecUnit)) {
                            boolean wasPrevBottle = "瓶".equals(previousUnit);
                            boolean isCurrentBottle = "瓶".equals(currentCreamFormulaSpecUnit);
                            
                            // 如果单位类型发生了"瓶"<->"非瓶"的切换，则需要重置
                            needReset = (wasPrevBottle && !isCurrentBottle) || (!wasPrevBottle && isCurrentBottle);
                        }
                        
                        // 如果需要重置，清除之前的选择并设置新的默认值
                        if (needReset) {
                            if ("瓶".equals(currentCreamFormulaSpecUnit)) {
                                // 切换到"瓶"单位，重置为30g
                                pretimeDoseEt.setText("30");
                            } else {
                                // 切换到非"瓶"单位（如"袋"、"勺"等），重置为1个单位
                                selectedCreamFormulaUnitCount = 1;
                            }
                        }
                        
                        updateCreamFormulaDosageDisplay();
                    }
                    
                    // 打印 packageSpecList 的 JSON 数据
                    if (mFormDetailMsg != null && mFormDetailMsg.getPackageSpecList() != null) {
                        LogUtils.d(MedicationFragment.class, "选中规格: " + popItem.getName());
                        LogUtils.d(MedicationFragment.class, "packageSpecList JSON数据: " +
                            JSON.toJSONString(mFormDetailMsg.getPackageSpecList()));
                    }
                }
            });
        }
    }
    
    /**
     * 显示辅料选择弹窗
     */
    private void showAuxiliaryMaterialPop() {
        if (mAuxiliaryMaterialPopWindow == null) {
            mAuxiliaryMaterialPopWindow = new AuxiliaryMaterialPopWindow(mContext);
        }
        
        if (mFormDetailMsg != null && mFormDetailMsg.getMakeMaterialList() != null && !mFormDetailMsg.getMakeMaterialList().isEmpty()) {
            mAuxiliaryMaterialPopWindow.setMaterialList(mFormDetailMsg.getMakeMaterialList());
            mAuxiliaryMaterialPopWindow.setSelectedMaterials(selectedAuxiliaryMaterials, isNoAuxiliaryMaterial);
            mAuxiliaryMaterialPopWindow.setOnConfirmListener(new AuxiliaryMaterialPopWindow.OnConfirmListener() {
                @Override
                public void onConfirm(List<String> selectedMaterials, boolean isNoMaterial) {
                    selectedAuxiliaryMaterials = new ArrayList<>(selectedMaterials);
                    isNoAuxiliaryMaterial = isNoMaterial;
                    
                    // 更新显示文本
                    if (isNoMaterial) {
                        auxiliaryMaterialTv.setText("不添加辅料");
                    } else if (!selectedMaterials.isEmpty()) {
                        String displayText = TextUtils.join("、", selectedMaterials);
                        auxiliaryMaterialTv.setText(displayText);
                    } else {
                        auxiliaryMaterialTv.setText("请选择辅料");
                    }
                }
            });
            mAuxiliaryMaterialPopWindow.show(mLayoutRoot);
        }
    }
    
    /**
     * 显示蜜丸单位数量选择弹窗
     */
    private void showPillUnitCountPop() {
        if (!TextUtils.isEmpty(currentSpecUnit) && currentSpecGramPerUnit > 0) {
            List<PopItem> unitCountItems = new ArrayList<>();
            
            // 生成1、2、3个单位的选项
            for (int i = 1; i <= 3; i++) {
                PopItem item = new PopItem();
                double gram = i * currentSpecGramPerUnit;
                String name = String.format("%d%s(%sg)", i, currentSpecUnit, 
                        DecimalUtils.format(String.valueOf(gram), 1));
                item.setName(name);
                item.setValue(String.valueOf(i));
                unitCountItems.add(item);
            }
            
            // 重置弹窗状态
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(unitCountItems);
            mBottomPopWindow.setPopTitle("选择用量");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewVisible();
            mBottomPopWindow.setBottomText("自定义");
            
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    selectedUnitCount = Integer.parseInt(popItem.getValue());
                    pretimeDoseEt.setText(String.valueOf(selectedUnitCount));
                    updatePillGramDisplay();
                    mBottomPopWindow.dimissPop();
                }
            });
            
            // 设置自定义按钮点击事件
            mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                    mBottomPopWindow.dimissPop();
                    showCustomPillDosageDialog();
                }
            });
        } else {
            ToastUtils.showShortMsg(mContext, "请先选择规格");
        }
    }
    
    /**
     * 显示自定义蜜丸用量弹窗
     */
    private void showCustomPillDosageDialog() {
        // 每次都重新创建弹窗，确保显示最新的规格信息
        customPillDosageDialog = com.doctor.br.view.InputDialog.getInstance(getContext());
        customPillDosageDialog.setCustomLayout(R.layout.custom_pill_dosage_dialog_input);
        
        customPillDosageDialog.setPositiveText("保存");
        customPillDosageDialog.setNegativeText("取消");
        customPillDosageDialog.setTitle(String.format("自定义用量 (规格: %sg/%s)", 
                        DecimalUtils.format(String.valueOf(currentSpecGramPerUnit), 1), 
                        currentSpecUnit));
        customPillDosageDialog.setUnit(currentSpecUnit);
        
        customPillDosageDialog.setCalculationLogic(input ->{
                    try {
                        if (TextUtils.isEmpty(input)) {
                            return String.format("(%sg)", "0");
                        }
                        int unitCount = Integer.parseInt(input);
                        double totalGram = unitCount * currentSpecGramPerUnit;
                        return String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
                    }catch (NumberFormatException e){
                        return String.format("(%sg)", "0");
                    }
                });
        
        customPillDosageDialog.setOnButtonClickListener(new OnButtonClickListener() {
                    @Override
                    public void onPositiveClick(View view, CharSequence editText) {
                        try {
                            String input = editText.toString().trim();
                            if (TextUtils.isEmpty(input)) {
                                ToastUtils.showShortMsg(mContext, "请输入用量数量");
                                return;
                            }
                            
                            int unitCount = Integer.parseInt(input);
                            if (unitCount <= 0) {
                                ToastUtils.showShortMsg(mContext, "请输入大于0的数量");
                                return;
                            }
                            
                            // 更新选中的单位数量
                            selectedUnitCount = unitCount;
                            pretimeDoseEt.setText(String.valueOf(selectedUnitCount));
                            updatePillGramDisplay();
                            
                            // 隐藏键盘并关闭弹窗
                            customPillDosageDialog.hideKeyboard();
                            customPillDosageDialog.dismiss();
                            
                        } catch (NumberFormatException e) {
                            ToastUtils.showShortMsg(mContext, "请输入有效的数字");
                        }
                    }
                    
                    @Override
                    public void onNegativeClick(View view, CharSequence editText) {
                        customPillDosageDialog.hideKeyboard();
                        customPillDosageDialog.dismiss();
                    }
                });
        
        customPillDosageDialog.show();
    }
    
    /**
     * 显示胶囊单位数量选择弹窗
     */
    private void showCapsuleUnitCountPop() {
        if (!TextUtils.isEmpty(currentCapsuleSpecUnit) && currentCapsuleSpecGramPerUnit > 0) {
            List<PopItem> unitCountItems = new ArrayList<>();
            
            // 生成3、6、9个单位的选项（胶囊的默认选项）
            int[] capsuleOptions = {3, 6, 9};
            for (int count : capsuleOptions) {
                PopItem item = new PopItem();
                double gram = count * currentCapsuleSpecGramPerUnit;
                String name = String.format("%d%s(%sg)", count, currentCapsuleSpecUnit, 
                        DecimalUtils.format(String.valueOf(gram), 1));
                item.setName(name);
                item.setValue(String.valueOf(count));
                unitCountItems.add(item);
            }
            
            // 重置弹窗状态
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(unitCountItems);
            mBottomPopWindow.setPopTitle("选择用量");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewVisible();
            mBottomPopWindow.setBottomText("自定义");
            
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    selectedCapsuleUnitCount = Integer.parseInt(popItem.getValue());
                    pretimeDoseEt.setText(String.valueOf(selectedCapsuleUnitCount));
                    updateCapsuleGramDisplay();
                    mBottomPopWindow.dimissPop();
                }
            });
            
            // 设置自定义按钮点击事件
            mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                    mBottomPopWindow.dimissPop();
                    showCustomCapsuleDosageDialog();
                }
            });
        } else {
            ToastUtils.showShortMsg(mContext, "请先选择规格");
        }
    }
    
    /**
     * 显示自定义胶囊用量弹窗
     */
    private void showCustomCapsuleDosageDialog() {
        // 每次都重新创建弹窗，确保显示最新的规格信息
        customCapsuleDosageDialog = com.doctor.br.view.InputDialog.getInstance(getContext());
        customCapsuleDosageDialog.setCustomLayout(R.layout.custom_pill_dosage_dialog_input);
        
        customCapsuleDosageDialog.setPositiveText("保存");
        customCapsuleDosageDialog.setNegativeText("取消");
        customCapsuleDosageDialog.setTitle(String.format("自定义用量 (规格: %sg/%s)", 
                        DecimalUtils.format(String.valueOf(currentCapsuleSpecGramPerUnit), 1), 
                        currentCapsuleSpecUnit));
        customCapsuleDosageDialog.setUnit(currentCapsuleSpecUnit);
        
        customCapsuleDosageDialog.setCalculationLogic(input ->{
                    try {
                        if (TextUtils.isEmpty(input)) {
                            return String.format("(%sg)", "0");
                        }
                        int unitCount = Integer.parseInt(input);
                        double totalGram = unitCount * currentCapsuleSpecGramPerUnit;
                        return String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
                    }catch (NumberFormatException e){
                        return String.format("(%sg)", "0");
                    }
                });
        
        customCapsuleDosageDialog.setOnButtonClickListener(new OnButtonClickListener() {
                    @Override
                    public void onPositiveClick(View view, CharSequence editText) {
                        try {
                            String input = editText.toString().trim();
                            if (TextUtils.isEmpty(input)) {
                                ToastUtils.showShortMsg(mContext, "请输入用量数量");
                                return;
                            }
                            
                            int unitCount = Integer.parseInt(input);
                            if (unitCount <= 0) {
                                ToastUtils.showShortMsg(mContext, "请输入大于0的数量");
                                return;
                            }
                            
                            // 更新选中的单位数量
                            selectedCapsuleUnitCount = unitCount;
                            pretimeDoseEt.setText(String.valueOf(selectedCapsuleUnitCount));
                            updateCapsuleGramDisplay();
                            
                            // 隐藏键盘并关闭弹窗
                            customCapsuleDosageDialog.hideKeyboard();
                            customCapsuleDosageDialog.dismiss();
                            
                        } catch (NumberFormatException e) {
                            ToastUtils.showShortMsg(mContext, "请输入有效的数字");
                        }
                    }
                    
                    @Override
                    public void onNegativeClick(View view, CharSequence editText) {
                        customCapsuleDosageDialog.hideKeyboard();
                        customCapsuleDosageDialog.dismiss();
                    }
                });
        
        customCapsuleDosageDialog.show();
    }
    
    /**
     * 显示水丸单位数量选择弹窗
     */
    private void showWaterPillUnitCountPop() {
        if (!TextUtils.isEmpty(currentWaterPillSpecUnit) && currentWaterPillSpecGramPerUnit > 0) {
            List<PopItem> unitCountItems = new ArrayList<>();
            
            // 生成1、2、3个单位的选项（与蜜丸一致）
            for (int i = 1; i <= 3; i++) {
                PopItem item = new PopItem();
                double gram = i * currentWaterPillSpecGramPerUnit;
                String name = String.format("%d%s(%sg)", i, currentWaterPillSpecUnit, 
                        DecimalUtils.format(String.valueOf(gram), 1));
                item.setName(name);
                item.setValue(String.valueOf(i));
                unitCountItems.add(item);
            }
            
            // 重置弹窗状态
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(unitCountItems);
            mBottomPopWindow.setPopTitle("选择用量");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewVisible();
            mBottomPopWindow.setBottomText("自定义");
            
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    selectedWaterPillUnitCount = Integer.parseInt(popItem.getValue());
                    pretimeDoseEt.setText(String.valueOf(selectedWaterPillUnitCount));
                    updateWaterPillGramDisplay();
                    mBottomPopWindow.dimissPop();
                }
            });
            
            // 设置自定义按钮点击事件
            mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                    mBottomPopWindow.dimissPop();
                    showCustomWaterPillDosageDialog();
                }
            });
        } else {
            ToastUtils.showShortMsg(mContext, "请先选择规格");
        }
    }
    
    /**
     * 显示自定义水丸用量弹窗
     */
    private void showCustomWaterPillDosageDialog() {
        // 每次都重新创建弹窗，确保显示最新的规格信息
        customWaterPillDosageDialog = com.doctor.br.view.InputDialog.getInstance(getContext());
        customWaterPillDosageDialog.setCustomLayout(R.layout.custom_pill_dosage_dialog_input);
        
        customWaterPillDosageDialog.setPositiveText("保存");
        customWaterPillDosageDialog.setNegativeText("取消");
        customWaterPillDosageDialog.setTitle(String.format("自定义用量 (规格: %sg/%s)", 
                        DecimalUtils.format(String.valueOf(currentWaterPillSpecGramPerUnit), 1), 
                        currentWaterPillSpecUnit));
        customWaterPillDosageDialog.setUnit(currentWaterPillSpecUnit);
        
        customWaterPillDosageDialog.setCalculationLogic(input ->{
                    try {
                        if (TextUtils.isEmpty(input)) {
                            return String.format("(%sg)", "0");
                        }
                        int unitCount = Integer.parseInt(input);
                        double totalGram = unitCount * currentWaterPillSpecGramPerUnit;
                        return String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
                    }catch (NumberFormatException e){
                        return String.format("(%sg)", "0");
                    }
                });
        
        customWaterPillDosageDialog.setOnButtonClickListener(new OnButtonClickListener() {
                    @Override
                    public void onPositiveClick(View view, CharSequence editText) {
                        try {
                            String input = editText.toString().trim();
                            if (TextUtils.isEmpty(input)) {
                                ToastUtils.showShortMsg(mContext, "请输入用量数量");
                                return;
                            }
                            
                            int unitCount = Integer.parseInt(input);
                            if (unitCount <= 0) {
                                ToastUtils.showShortMsg(mContext, "请输入大于0的数量");
                                return;
                            }
                            
                            // 更新选中的单位数量
                            selectedWaterPillUnitCount = unitCount;
                            pretimeDoseEt.setText(String.valueOf(selectedWaterPillUnitCount));
                            updateWaterPillGramDisplay();
                            
                            // 隐藏键盘并关闭弹窗
                            customWaterPillDosageDialog.hideKeyboard();
                            customWaterPillDosageDialog.dismiss();
                            
                        } catch (NumberFormatException e) {
                            ToastUtils.showShortMsg(mContext, "请输入有效的数字");
                        }
                    }
                    
                    @Override
                    public void onNegativeClick(View view, CharSequence editText) {
                        customWaterPillDosageDialog.hideKeyboard();
                        customWaterPillDosageDialog.dismiss();
                    }
                });
        
        customWaterPillDosageDialog.show();
    }
    
    /**
     * 显示膏方单位数量选择弹窗
     */
    private void showCreamFormulaUnitCountPop() {
        if (!TextUtils.isEmpty(currentCreamFormulaSpecUnit) && currentCreamFormulaSpecGramPerUnit > 0) {
            List<PopItem> unitCountItems = new ArrayList<>();
            
            // 生成1、2、3个单位的选项
            for (int i = 1; i <= 3; i++) {
                double totalGram = i * currentCreamFormulaSpecGramPerUnit;
                String gramDisplay = DecimalUtils.format(String.valueOf(totalGram), 1);
                
                PopItem item = new PopItem();
                item.setName(String.format("%d%s(%sg)", i, currentCreamFormulaSpecUnit, gramDisplay));
                item.setValue(String.valueOf(i));
                unitCountItems.add(item);
            }
            
            mBottomPopWindow.resetState();
            mBottomPopWindow.setPopContentData(unitCountItems);
            mBottomPopWindow.setPopTitle("选择用量");
            mBottomPopWindow.showPop();
            mBottomPopWindow.setBottomViewVisible();
            mBottomPopWindow.setBottomText("自定义");
            
            // 设置选项点击事件
            mBottomPopWindow.setOnItemClickListener(new BottomPopWindow.OnPopItemClickListener() {
                @Override
                public void onPopItemClick(int position, PopItem popItem) {
                    try {
                        selectedCreamFormulaUnitCount = Integer.parseInt(popItem.getValue());
                        pretimeDoseEt.setText(String.valueOf(selectedCreamFormulaUnitCount));
                        updateCreamFormulaGramDisplay();
                        mBottomPopWindow.dimissPop();
                    } catch (NumberFormatException e) {
                        e.printStackTrace();
                    }
                }
            });
            
            // 设置自定义按钮点击事件
            mBottomPopWindow.setOnBottomViewClickListener(new BottomPopWindow.OnBottomViewClickListener() {
                @Override
                public void onBottomViewClick(boolean isDelete, List<PopItem> selectedItems) {
                    mBottomPopWindow.dimissPop();
                    showCustomCreamFormulaDosageDialog();
                }
            });
        } else {
            ToastUtils.showShortMsg(mContext, "请先选择规格");
        }
    }
    
    /**
     * 显示自定义膏方用量弹窗
     */
    private void showCustomCreamFormulaDosageDialog() {
        // 每次都重新创建弹窗，确保显示最新的规格信息
        customCreamFormulaDosageDialog = com.doctor.br.view.InputDialog.getInstance(getContext());
        customCreamFormulaDosageDialog.setCustomLayout(R.layout.custom_pill_dosage_dialog_input);
        
        customCreamFormulaDosageDialog.setPositiveText("保存");
        customCreamFormulaDosageDialog.setNegativeText("取消");
        customCreamFormulaDosageDialog.setTitle(String.format("自定义用量 (规格: %sg/%s)", 
                        DecimalUtils.format(String.valueOf(currentCreamFormulaSpecGramPerUnit), 1), 
                        currentCreamFormulaSpecUnit));
        customCreamFormulaDosageDialog.setUnit(currentCreamFormulaSpecUnit);
        
        customCreamFormulaDosageDialog.setCalculationLogic(input ->{
                    try {
                        if (TextUtils.isEmpty(input)) {
                            return String.format("(%sg)", "0");
                        }
                        int unitCount = Integer.parseInt(input);
                        double totalGram = unitCount * currentCreamFormulaSpecGramPerUnit;
                        return String.format("(%sg)", DecimalUtils.format(String.valueOf(totalGram), 1));
                    }catch (NumberFormatException e){
                        return String.format("(%sg)", "0");
                    }
                });
        
        customCreamFormulaDosageDialog.setOnButtonClickListener(new OnButtonClickListener() {
                    @Override
                    public void onPositiveClick(View view, CharSequence editText) {
                        try {
                            String input = editText.toString().trim();
                            if (TextUtils.isEmpty(input)) {
                                ToastUtils.showShortMsg(mContext, "请输入用量数量");
                                return;
                            }
                            
                            int unitCount = Integer.parseInt(input);
                            if (unitCount <= 0) {
                                ToastUtils.showShortMsg(mContext, "请输入大于0的数量");
                                return;
                            }
                            
                            // 更新选中的单位数量
                            selectedCreamFormulaUnitCount = unitCount;
                            pretimeDoseEt.setText(String.valueOf(selectedCreamFormulaUnitCount));
                            updateCreamFormulaGramDisplay();
                            
                            // 隐藏键盘并关闭弹窗
                            customCreamFormulaDosageDialog.hideKeyboard();
                            customCreamFormulaDosageDialog.dismiss();
                            
                        } catch (NumberFormatException e) {
                            ToastUtils.showShortMsg(mContext, "请输入有效的数字");
                        }
                    }
                    
                    @Override
                    public void onNegativeClick(View view, CharSequence editText) {
                        customCreamFormulaDosageDialog.hideKeyboard();
                        customCreamFormulaDosageDialog.dismiss();
                    }
                });
        
        customCreamFormulaDosageDialog.show();
    }




}
